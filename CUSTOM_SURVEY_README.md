# Custom Survey Metrics System

This system provides dynamic calculation of survey metrics for custom questions stored in the `employee_questions` table. It automatically categorizes questions using AI and calculates various metrics similar to the reference Survey folder implementation.

## Key Features

### 1. Dynamic Question Categorization
- Uses AI (CrewAI agents) to automatically categorize questions into predefined categories
- Supports both survey metrics categories (diversity, equity, inclusion, etc.) and action categories (communication, leadership, etc.)
- Falls back to keyword matching if AI categorization fails

### 2. Employee Voice Statement Matching
- Automatically matches survey questions to 13 predefined employee voice statements
- Uses AI to determine which voice statement each question relates to
- Calculates voice scores and priorities based on negative/neutral percentages

### 3. Demographic Analysis
- Department-wise metrics (HR, Finance, Sales, etc.)
- Experience-level metrics (0-1 year, 1-3 years, etc.)
- Gender-based metrics (Male, Female, Other)
- Role-based engagement metrics (Junior Staff, Senior Staff, etc.)

### 4. Comprehensive Scoring
- Uses same scoring algorithm as reference implementation:
  - Positive sentiment: +1.0 weight
  - Neutral sentiment: +0.5 weight  
  - Negative sentiment: -0.1 weight

## Files Structure

### Core Calculation Files
- `custom_survey_metrics.py` - Main survey metrics calculation logic
- `custom_action_calculations.py` - Action table metrics calculation
- `custom_metrics_storage.py` - Database storage functions
- `custom_table_creation.py` - Database table creation

### Database Tables
- `custom_survey_scores` - Stores survey metrics (DEI, categories, demographics)
- `custom_action_scores` - Stores action metrics (communication, leadership, etc.)
- `employee_questions` - Source table with custom questions per email
- `student_data` - Survey responses with sentiment analysis

## How It Works

### 1. Survey Submission Process
1. User submits survey responses via `/test/<email>`
2. Responses are stored in `student_data` table with sentiment analysis
3. System automatically calculates metrics using custom questions for that email
4. Metrics are stored in `custom_survey_scores` and `custom_action_scores` tables

### 2. Dynamic Question Mapping
```python
# Example: Questions are automatically categorized
question = "Do you feel valued and appreciated at work?"
categories = ["recognition", "engagement", "workplace_satisfaction"]
voice_statement = 9  # "My work is recognized, and I feel appreciated"
```

### 3. Metric Calculation
- **Survey Metrics**: DEI scores, category scores, demographic breakdowns
- **Action Metrics**: Actionable insights for HR/management
- **Voice Metrics**: Employee voice statement priorities

### 4. Viewing Results
- Access calculated metrics via `/metrics/<email>`
- Shows comprehensive breakdown of all calculated scores

## Usage Examples

### 1. Taking a Survey
```
GET /test/<EMAIL>
```
- Shows user info form, then custom <NAME_EMAIL>
- Questions are pulled from `employee_questions` table

### 2. Viewing Metrics
```
GET /metrics/<EMAIL>
```
- Shows calculated <NAME_EMAIL>
- Includes survey metrics, action metrics, and demographic breakdowns

## Key Differences from Reference Implementation

### 1. Dynamic vs Hardcoded Questions
- **Reference**: Fixed 20 questions with hardcoded category mappings
- **Custom**: Variable questions per email with AI-based categorization

### 2. Question Source
- **Reference**: Questions defined in Python arrays
- **Custom**: Questions stored in `employee_questions` database table

### 3. Category Mapping
- **Reference**: Static question number arrays (e.g., `DIVERSITY_QUESTIONS = [1, 2, 3]`)
- **Custom**: Dynamic mapping based on AI analysis and keywords

### 4. Employee Voice Matching
- **Reference**: Hardcoded mapping of questions to voice statements
- **Custom**: AI-powered matching of questions to voice statements

## Configuration

### Database Configuration
```python
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}
```

### Category Keywords
Categories are determined using both AI analysis and keyword matching:
```python
CATEGORY_KEYWORDS = {
    'diversity': ['diversity', 'hire', 'promote', 'equal chance'],
    'equity': ['equity', 'fair', 'equal treatment', 'compensation'],
    'inclusion': ['inclusion', 'valued', 'appreciated', 'trust', 'belonging']
}
```

## Error Handling

- If AI categorization fails, falls back to keyword matching
- If calculations fail, survey submission still succeeds
- Graceful handling of missing questions or empty categories
- Default values (0.00) for missing demographic groups

## Future Enhancements

1. **Real-time Category Learning**: System could learn from manual category corrections
2. **Custom Voice Statements**: Allow custom voice statements per organization
3. **Advanced Analytics**: Trend analysis, comparative metrics
4. **API Endpoints**: RESTful API for external integrations

## Testing

To test the system:
1. Add questions to `employee_questions` table for a test email
2. Take survey via `/test/<email>`
3. View results via `/metrics/<email>`
4. Check database tables for stored metrics

The system maintains compatibility with the reference implementation's scoring methodology while providing flexibility for custom question sets.
