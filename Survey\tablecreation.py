import mysql.connector

def create_company_scores_table(DB_CONFIG):

    # SQL query to create the companyscores table
    create_table_query = '''
    CREATE TABLE IF NOT EXISTS companyscore (
    -- Basic Company Information
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VA<PERSON>HAR(255) NOT NULL,
    industry_type VARCHAR(255),
    country VARCHAR(255),
    product_name VARCHAR(255),
    leader_name VA<PERSON>HAR(255),
    leader_role VARCHAR(255),
    contact_email VARCHAR(255),
    survey_start_date VARCHAR(20),
    survey_end_date VARCHAR(20),
    payment_status VARCHAR(50),
    survey_url VARCHAR(255) UNIQUE,
    num_surveys INT,
      -- Survey Response Metrics
    total_responses INT,
    response_rate DECIMAL(5,2),
    profile_pics VARCHAR(1000),
    validity_data VARCHAR(8),
    
    -- Overall DEI Metrics
    dei_score DECIMAL(5,2),
    dei_positive_pct DECIMAL(5,2),
    dei_neutral_pct DECIMAL(5,2),
    dei_negative_pct DECIMAL(5,2),
    
    -- Individual DEI Component Metrics
    diversity_score DECIMAL(5,2),
    diversity_positive_pct DECIMAL(5,2),
    diversity_neutral_pct DECIMAL(5,2),
    diversity_negative_pct DECIMAL(5,2),
    
    equity_score DECIMAL(5,2),
    equity_positive_pct DECIMAL(5,2),
    equity_neutral_pct DECIMAL(5,2),
    equity_negative_pct DECIMAL(5,2),
      inclusion_score DECIMAL(5,2),
    inclusion_positive_pct DECIMAL(5,2),
    inclusion_neutral_pct DECIMAL(5,2),
    inclusion_negative_pct DECIMAL(5,2),
    
    -- Strategic Alignment Gender Distribution
    strategic_alignment_positive_male_pct DECIMAL(5,2),
    strategic_alignment_positive_female_pct DECIMAL(5,2),
    strategic_alignment_positive_other_pct DECIMAL(5,2),
    strategic_alignment_neutral_male_pct DECIMAL(5,2),
    strategic_alignment_neutral_female_pct DECIMAL(5,2),
    strategic_alignment_neutral_other_pct DECIMAL(5,2),
    strategic_alignment_negative_male_pct DECIMAL(5,2),
    strategic_alignment_negative_female_pct DECIMAL(5,2),
    strategic_alignment_negative_other_pct DECIMAL(5,2),
    
    -- Engagement Metrics
    culture_engagement_score DECIMAL(5,2),
    culture_engagement_positive_pct DECIMAL(5,2),
    culture_engagement_neutral_pct DECIMAL(5,2),
    culture_engagement_negative_pct DECIMAL(5,2),
    
    strategic_alignment_score DECIMAL(5,2),
    strategic_alignment_positive_pct DECIMAL(5,2),
    strategic_alignment_neutral_pct DECIMAL(5,2),
    strategic_alignment_negative_pct DECIMAL(5,2),
    
    support_motivation_score DECIMAL(5,2),
    support_motivation_positive_pct DECIMAL(5,2),
    support_motivation_neutral_pct DECIMAL(5,2),
    support_motivation_negative_pct DECIMAL(5,2),
    
    skill_development_score DECIMAL(5,2),
    skill_development_positive_pct DECIMAL(5,2),
    skill_development_neutral_pct DECIMAL(5,2),
    skill_development_negative_pct DECIMAL(5,2),
    
    engagement_rate_score DECIMAL(5,2),
    engagement_rate_positive_pct DECIMAL(5,2),
    engagement_rate_neutral_pct DECIMAL(5,2),
    engagement_rate_negative_pct DECIMAL(5,2),
    
    -- Leadership, Policies, and Workplace Culture Metrics
    leadership_score DECIMAL(5,2),
    leadership_positive_pct DECIMAL(5,2),
    leadership_neutral_pct DECIMAL(5,2),
    leadership_negative_pct DECIMAL(5,2),
    
    policies_score DECIMAL(5,2),
    policies_positive_pct DECIMAL(5,2),
    policies_neutral_pct DECIMAL(5,2),
    policies_negative_pct DECIMAL(5,2),
    
    workplace_culture_score DECIMAL(5,2),
    workplace_culture_positive_pct DECIMAL(5,2),
    workplace_culture_neutral_pct DECIMAL(5,2),
    workplace_culture_negative_pct DECIMAL(5,2),
    
    -- Various Category Metrics
    credibility_score DECIMAL(5,2),
    credibility_positive_pct DECIMAL(5,2),
    credibility_neutral_pct DECIMAL(5,2),
    credibility_negative_pct DECIMAL(5,2),
    
    fairness_score DECIMAL(5,2),
    fairness_positive_pct DECIMAL(5,2),
    fairness_neutral_pct DECIMAL(5,2),
    fairness_negative_pct DECIMAL(5,2),
    
    workplace_satisfaction_score DECIMAL(5,2),
    workplace_satisfaction_positive_pct DECIMAL(5,2),
    workplace_satisfaction_neutral_pct DECIMAL(5,2),
    workplace_satisfaction_negative_pct DECIMAL(5,2),
    
    team_spirit_score DECIMAL(5,2),
    team_spirit_positive_pct DECIMAL(5,2),
    team_spirit_neutral_pct DECIMAL(5,2),
    team_spirit_negative_pct DECIMAL(5,2),
    
    well_being_score DECIMAL(5,2),
    well_being_positive_pct DECIMAL(5,2),
    well_being_neutral_pct DECIMAL(5,2),
    well_being_negative_pct DECIMAL(5,2),
    
    respect_score DECIMAL(5,2),
    respect_positive_pct DECIMAL(5,2),
    respect_neutral_pct DECIMAL(5,2),
    respect_negative_pct DECIMAL(5,2),
    
    open_communication_score DECIMAL(5,2),
    open_communication_positive_pct DECIMAL(5,2),
    open_communication_neutral_pct DECIMAL(5,2),
    open_communication_negative_pct DECIMAL(5,2),
    
    recognition_score DECIMAL(5,2),
    recognition_positive_pct DECIMAL(5,2),
    recognition_neutral_pct DECIMAL(5,2),
    recognition_negative_pct DECIMAL(5,2),
    
    motivation_score DECIMAL(5,2),
    motivation_positive_pct DECIMAL(5,2),
    motivation_neutral_pct DECIMAL(5,2),
    motivation_negative_pct DECIMAL(5,2),
    
    -- Additional Metrics
    proud_to_work_score DECIMAL(5,2),
    proud_to_work_positive_pct DECIMAL(5,2),
    proud_to_work_neutral_pct DECIMAL(5,2),
    proud_to_work_negative_pct DECIMAL(5,2),
    
    people_care_score DECIMAL(5,2),
    people_care_positive_pct DECIMAL(5,2),
    people_care_neutral_pct DECIMAL(5,2),
    people_care_negative_pct DECIMAL(5,2),
    
    fair_promotion_score DECIMAL(5,2),
    fair_promotion_positive_pct DECIMAL(5,2),
    fair_promotion_neutral_pct DECIMAL(5,2),
    fair_promotion_negative_pct DECIMAL(5,2),
    
    involvement_decision_score DECIMAL(5,2),
    involvement_decision_positive_pct DECIMAL(5,2),
    involvement_decision_neutral_pct DECIMAL(5,2),
    involvement_decision_negative_pct DECIMAL(5,2),
    
    leadership_reachable_score DECIMAL(5,2),
    leadership_reachable_positive_pct DECIMAL(5,2),
    leadership_reachable_neutral_pct DECIMAL(5,2),
    leadership_reachable_negative_pct DECIMAL(5,2),
    
    -- Department-wise Metrics
    dept_HR_and_Admin_dei_score DECIMAL(5,2),
    dept_HR_and_Admin_positive_pct DECIMAL(5,2),
    dept_HR_and_Admin_neutral_pct DECIMAL(5,2),
    dept_HR_and_Admin_negative_pct DECIMAL(5,2),
    
    dept_Finance_and_accounting_dei_score DECIMAL(5,2),
    dept_Finance_and_accounting_positive_pct DECIMAL(5,2),
    dept_Finance_and_accounting_neutral_pct DECIMAL(5,2),
    dept_Finance_and_accounting_negative_pct DECIMAL(5,2),
    
    dept_Sales_marketing_dei_score DECIMAL(5,2),
    dept_Sales_marketing_positive_pct DECIMAL(5,2),
    dept_Sales_marketing_neutral_pct DECIMAL(5,2),
    dept_Sales_marketing_negative_pct DECIMAL(5,2),
    
    dept_Product_development_dei_score DECIMAL(5,2),
    dept_Product_development_positive_pct DECIMAL(5,2),
    dept_Product_development_neutral_pct DECIMAL(5,2),    
    dept_Product_development_negative_pct DECIMAL(5,2),
    
    dept_Technical_dei_score DECIMAL(5,2),
    dept_Technical_positive_pct DECIMAL(5,2),
    dept_Technical_neutral_pct DECIMAL(5,2),
    dept_Technical_negative_pct DECIMAL(5,2),
    
    dept_Operations_dei_score DECIMAL(5,2),
    dept_Operations_positive_pct DECIMAL(5,2),
    dept_Operations_neutral_pct DECIMAL(5,2),
    dept_Operations_negative_pct DECIMAL(5,2),
    
    dept_Procurement_dei_score DECIMAL(5,2),
    dept_Procurement_positive_pct DECIMAL(5,2),
    dept_Procurement_neutral_pct DECIMAL(5,2),
    dept_Procurement_negative_pct DECIMAL(5,2),
    
    dept_Quality_dei_score DECIMAL(5,2),
    dept_Quality_positive_pct DECIMAL(5,2),
    dept_Quality_neutral_pct DECIMAL(5,2),
    dept_Quality_negative_pct DECIMAL(5,2),
    
    dept_Business_Development_dei_score DECIMAL(5,2),
    dept_Business_Development_positive_pct DECIMAL(5,2),
    dept_Business_Development_neutral_pct DECIMAL(5,2),
    dept_Business_Development_negative_pct DECIMAL(5,2),
    
    dept_executive_dei_score DECIMAL(5,2),
    dept_executive_positive_pct DECIMAL(5,2),
    dept_executive_neutral_pct DECIMAL(5,2),
    dept_executive_negative_pct DECIMAL(5,2),
    
    dept_leadership_dei_score DECIMAL(5,2),
    dept_leadership_positive_pct DECIMAL(5,2),
    dept_leadership_neutral_pct DECIMAL(5,2),
    dept_leadership_negative_pct DECIMAL(5,2),
    
    dept_Management_dei_score DECIMAL(5,2),
    dept_Management_positive_pct DECIMAL(5,2),
    dept_Management_neutral_pct DECIMAL(5,2),
    dept_Management_negative_pct DECIMAL(5,2),
    
    dept_others_dei_score DECIMAL(5,2),
    dept_others_positive_pct DECIMAL(5,2),
    dept_others_neutral_pct DECIMAL(5,2),
    dept_others_negative_pct DECIMAL(5,2),
    
    -- Experience-wise Metrics
    exp_30dto1_dei_score DECIMAL(5,2),
    exp_30dto1_positive_pct DECIMAL(5,2),
    exp_30dto1_neutral_pct DECIMAL(5,2),
    exp_30to1_negative_pct DECIMAL(5,2),

    exp_1to3_dei_score DECIMAL(5,2),
    exp_1to3_positive_pct DECIMAL(5,2),
    exp_1to3_neutral_pct DECIMAL(5,2),
    exp_1to3_negative_pct DECIMAL(5,2),
    
    exp_3to5_dei_score DECIMAL(5,2),
    exp_3to5_positive_pct DECIMAL(5,2),
    exp_3to5_neutral_pct DECIMAL(5,2),
    exp_3to5_negative_pct DECIMAL(5,2),
    
    exp_above5_dei_score DECIMAL(5,2),
    exp_above5_positive_pct DECIMAL(5,2),
    exp_above5_neutral_pct DECIMAL(5,2),
    exp_above5_negative_pct DECIMAL(5,2),
    
    -- Role-wise Metrics
    role_Junior_staff_engagement_score DECIMAL(5,2),
    role_Junior_staff_positive_pct DECIMAL(5,2),
    role_Junior_staff_neutral_pct DECIMAL(5,2),
    role_Junior_staff_negative_pct DECIMAL(5,2),
    
    role_Senior_staff_engagement_score DECIMAL(5,2),
    role_Senior_staff_positive_pct DECIMAL(5,2),
    role_Senior_staff_neutral_pct DECIMAL(5,2),
    role_Senior_staff_negative_pct DECIMAL(5,2),
    
    role_manager_engagement_score DECIMAL(5,2),
    role_manager_positive_pct DECIMAL(5,2),
    role_manager_neutral_pct DECIMAL(5,2),
    role_manager_negative_pct DECIMAL(5,2),
    
    role_executive_engagement_score DECIMAL(5,2),
    role_executive_positive_pct DECIMAL(5,2),
    role_executive_neutral_pct DECIMAL(5,2),
    role_executive_negative_pct DECIMAL(5,2),
    
    role_trainee_engagement_score DECIMAL(5,2),
    role_trainee_positive_pct DECIMAL(5,2),
    role_trainee_neutral_pct DECIMAL(5,2),
    role_trainee_negative_pct DECIMAL(5,2),
    
    role_team_member_engagement_score DECIMAL(5,2),
    role_team_member_positive_pct DECIMAL(5,2),
    role_team_member_neutral_pct DECIMAL(5,2),
    role_team_member_negative_pct DECIMAL(5,2),
    
    -- Gender-wise Metrics
    gender_male_dei_score DECIMAL(5,2),
    gender_male_positive_pct DECIMAL(5,2),
    gender_male_neutral_pct DECIMAL(5,2),
    gender_male_negative_pct DECIMAL(5,2),
    
    gender_female_dei_score DECIMAL(5,2),
    gender_female_positive_pct DECIMAL(5,2),
    gender_female_neutral_pct DECIMAL(5,2),
    gender_female_negative_pct DECIMAL(5,2),
    
    gender_others_dei_score DECIMAL(5,2),
    gender_others_positive_pct DECIMAL(5,2),
    gender_others_neutral_pct DECIMAL(5,2),
    gender_others_negative_pct DECIMAL(5,2),
    
    -- Employee Voice Metrics (for all 13 statements)
    emp_voice1_score DECIMAL(5,2),
    emp_voice1_positive_pct DECIMAL(5,2),
    emp_voice1_neutral_pct DECIMAL(5,2),
    emp_voice1_negative_pct DECIMAL(5,2),
    emp_voice1_priority VARCHAR(50),
    
    emp_voice2_score DECIMAL(5,2),
    emp_voice2_positive_pct DECIMAL(5,2),
    emp_voice2_neutral_pct DECIMAL(5,2),
    emp_voice2_negative_pct DECIMAL(5,2),
    emp_voice2_priority VARCHAR(50),
    
    emp_voice3_score DECIMAL(5,2),
    emp_voice3_positive_pct DECIMAL(5,2),
    emp_voice3_neutral_pct DECIMAL(5,2),
    emp_voice3_negative_pct DECIMAL(5,2),
    emp_voice3_priority VARCHAR(50),
    
    emp_voice4_score DECIMAL(5,2),
    emp_voice4_positive_pct DECIMAL(5,2),
    emp_voice4_neutral_pct DECIMAL(5,2),
    emp_voice4_negative_pct DECIMAL(5,2),
    emp_voice4_priority VARCHAR(50),
    
    emp_voice5_score DECIMAL(5,2),
    emp_voice5_positive_pct DECIMAL(5,2),
    emp_voice5_neutral_pct DECIMAL(5,2),
    emp_voice5_negative_pct DECIMAL(5,2),
    emp_voice5_priority VARCHAR(50),
    
    emp_voice6_score DECIMAL(5,2),
    emp_voice6_positive_pct DECIMAL(5,2),
    emp_voice6_neutral_pct DECIMAL(5,2),
    emp_voice6_negative_pct DECIMAL(5,2),
    emp_voice6_priority VARCHAR(50),
    
    emp_voice7_score DECIMAL(5,2),
    emp_voice7_positive_pct DECIMAL(5,2),
    emp_voice7_neutral_pct DECIMAL(5,2),
    emp_voice7_negative_pct DECIMAL(5,2),
    emp_voice7_priority VARCHAR(50),
    
    emp_voice8_score DECIMAL(5,2),
    emp_voice8_positive_pct DECIMAL(5,2),
    emp_voice8_neutral_pct DECIMAL(5,2),
    emp_voice8_negative_pct DECIMAL(5,2),
    emp_voice8_priority VARCHAR(50),
    
    emp_voice9_score DECIMAL(5,2),
    emp_voice9_positive_pct DECIMAL(5,2),
    emp_voice9_neutral_pct DECIMAL(5,2),
    emp_voice9_negative_pct DECIMAL(5,2),
    emp_voice9_priority VARCHAR(50),
    
    emp_voice10_score DECIMAL(5,2),
    emp_voice10_positive_pct DECIMAL(5,2),
    emp_voice10_neutral_pct DECIMAL(5,2),
    emp_voice10_negative_pct DECIMAL(5,2),
    emp_voice10_priority VARCHAR(50),
    
    emp_voice11_score DECIMAL(5,2),
    emp_voice11_positive_pct DECIMAL(5,2),
    emp_voice11_neutral_pct DECIMAL(5,2),
    emp_voice11_negative_pct DECIMAL(5,2),
    emp_voice11_priority VARCHAR(50),
    
    emp_voice12_score DECIMAL(5,2),
    emp_voice12_positive_pct DECIMAL(5,2),
    emp_voice12_neutral_pct DECIMAL(5,2),
    emp_voice12_negative_pct DECIMAL(5,2),
    emp_voice12_priority VARCHAR(50),
    
    emp_voice13_score DECIMAL(5,2),
    emp_voice13_positive_pct DECIMAL(5,2),
    emp_voice13_neutral_pct DECIMAL(5,2),
    emp_voice13_negative_pct DECIMAL(5,2),    
    emp_voice13_priority VARCHAR(50),
    
    -- Timestamp
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
'''

    try:
        # Establish database connection
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Execute the create table query
        cursor.execute(create_table_query)
        print("✅Company scores table created successfully!")
        
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


# Note: This query creates a table with all columns needed for storing:
# 1. Basic company information
# 2. Survey response metrics
# 3. Overall DEI metrics
# 4. Individual DEI component metrics
# 5. Engagement metrics
# 6. Leadership, Policies, and Workplace Culture metrics
# 7. Various category metrics (credibility, fairness, etc.)
# 8. Department-wise metrics
# 9. Experience-wise metrics
# 10. Role-wise metrics
# 11. Gender-wise metrics
# 12. Employee voice metrics for all 13 statements



def student_data_table(DB_CONFIG):
    # Connect to the database
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # SQL query to create the response table
    create_table_query = '''
    CREATE TABLE IF NOT EXISTS student_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question_number INT,
        question_text TEXT,
        selected_text TEXT,
        form_url VARCHAR(500),
        unique_id VARCHAR(255),
        gender VARCHAR(50),
        age_group VARCHAR(50),
        tenure_group VARCHAR(50),
        role VARCHAR(255),
        department VARCHAR(255),
        predicted_sentiment VARCHAR(50)
    );
    '''

    # Execute and commit
    cursor.execute(create_table_query)
    conn.commit()
    print("Table 'employee_responses' created successfully.")

    # Clean up
    cursor.close()
    conn.close()

