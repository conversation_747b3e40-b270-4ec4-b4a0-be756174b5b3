from flask import Flask, request, render_template, redirect ,session
import mysql.connector
from crewai_agent import predict_sentiment, explain_sentiment, predict_category, predict_voice_statement
import pandas as pd
from decimal import Decimal

app = Flask(__name__)
app.secret_key = '********************************************************'

import uuid

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

# Create custom tables
from custom_table_creation import create_custom_survey_scores_table, create_custom_action_scores_table
create_custom_survey_scores_table(DB_CONFIG)
create_custom_action_scores_table(DB_CONFIG)

# MySQL connection
db = mysql.connector.connect(**DB_CONFIG)
cursor = db.cursor()

@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data
            try:
                for key in request.form:
                    if key.startswith('question_') and not key.startswith('question_text_'):
                        question_number = int(key.split('_')[1])
                        selected_option = request.form[key]
                        
                        # Check if question_text exists in form
                        question_text_key = f'question_text_{question_number}'
                        if question_text_key not in request.form:
                            return f"Error: Missing question text for question {question_number}", 400
                        
                        question_text = request.form[question_text_key]
                        
                        # Combine question and answer for better sentiment analysis
                        combined_text = f"{question_text} {selected_option}"

                        # Perform AI sentiment analysis
                        print(f"Analyzing sentiment for: {combined_text}")
                        predicted_sentiment = predict_sentiment(combined_text)
                        reason = explain_sentiment(combined_text)

                        print(f"Predicted Sentiment: {predicted_sentiment}")

                        cursor.execute("""
                            INSERT INTO student_data (
                                question_number, question_text, selected_text, form_url,
                                unique_id, gender, age_group, tenure_group, role, department, predicted_sentiment,reason
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)
                        """, (
                            question_number, question_text, selected_option, f"/test/{email}",
                            session['unique_id'], session['gender'], session['age_group'],
                            session['tenure_group'], session['role'], session['department'], predicted_sentiment,reason
                        ))

                db.commit()

                # After successful submission, calculate metrics
                try:
                    from custom_survey_metrics import calculate_all_custom_metrics
                    from custom_action_calculations import calculate_all_action_metrics
                    from custom_metrics_storage import store_all_custom_metrics

                    # Get survey responses for this form_url
                    form_url = f"/test/{email}"
                    cursor.execute("""
                        SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
                        FROM student_data
                        WHERE form_url = %s
                    """, (form_url,))

                    survey_data = cursor.fetchall()
                    if survey_data:
                        # Convert to DataFrame for calculations
                        survey_responses = pd.DataFrame(survey_data, columns=[
                            'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
                        ])

                        # Calculate survey metrics
                        survey_metrics = calculate_all_custom_metrics(email, survey_responses)

                        # Calculate action metrics
                        action_metrics = calculate_all_action_metrics(email, survey_responses)

                        # Store metrics in database
                        store_all_custom_metrics(email, form_url, survey_metrics, action_metrics)

                        print(f"Calculated and stored metrics for {email}")
                        print(f"Survey metrics: {survey_metrics['overall']}")
                        print(f"Action categories found: {list(action_metrics['action_metrics'].keys())}")

                except Exception as calc_error:
                    print(f"Error calculating metrics: {calc_error}")
                    # Don't fail the submission if calculations fail

                session.clear()
                return "Thank you! Test submitted successfully."
                
            except Exception as e:
                db.rollback()
                return f"Error submitting survey: {str(e)}", 500

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)

@app.route('/metrics/<email>')
def view_metrics(email):
    """View calculated metrics for an email"""
    try:
        form_url = f"/test/{email}"

        # Get survey metrics
        cursor.execute("""
            SELECT total_responses, response_rate, dei_score,
                   diversity_score, equity_score, inclusion_score, engagement_score
            FROM custom_survey_scores
            WHERE email = %s AND form_url = %s
        """, (email, form_url))

        survey_result = cursor.fetchone()

        # Get action metrics
        cursor.execute("""
            SELECT communication_score, leadership_effectiveness_score,
                   work_life_balance_score, career_development_score
            FROM custom_action_scores
            WHERE email = %s AND form_url = %s
        """, (email, form_url))

        action_result = cursor.fetchone()

        if survey_result or action_result:
            metrics_data = {
                'email': email,
                'survey_metrics': {
                    'total_responses': survey_result[0] if survey_result else 0,
                    'response_rate': float(survey_result[1]) if survey_result else 0,
                    'dei_score': float(survey_result[2]) if survey_result else 0,
                    'diversity_score': float(survey_result[3]) if survey_result else 0,
                    'equity_score': float(survey_result[4]) if survey_result else 0,
                    'inclusion_score': float(survey_result[5]) if survey_result else 0,
                    'engagement_score': float(survey_result[6]) if survey_result else 0,
                } if survey_result else {},
                'action_metrics': {
                    'communication_score': float(action_result[0]) if action_result else 0,
                    'leadership_effectiveness_score': float(action_result[1]) if action_result else 0,
                    'work_life_balance_score': float(action_result[2]) if action_result else 0,
                    'career_development_score': float(action_result[3]) if action_result else 0,
                } if action_result else {}
            }

            return f"""
            <h1>Metrics for {email}</h1>
            <h2>Survey Metrics:</h2>
            <ul>
                <li>Total Responses: {metrics_data['survey_metrics'].get('total_responses', 'N/A')}</li>
                <li>Response Rate: {metrics_data['survey_metrics'].get('response_rate', 'N/A')}%</li>
                <li>DEI Score: {metrics_data['survey_metrics'].get('dei_score', 'N/A')}</li>
                <li>Diversity Score: {metrics_data['survey_metrics'].get('diversity_score', 'N/A')}</li>
                <li>Equity Score: {metrics_data['survey_metrics'].get('equity_score', 'N/A')}</li>
                <li>Inclusion Score: {metrics_data['survey_metrics'].get('inclusion_score', 'N/A')}</li>
                <li>Engagement Score: {metrics_data['survey_metrics'].get('engagement_score', 'N/A')}</li>
            </ul>
            <h2>Action Metrics:</h2>
            <ul>
                <li>Communication Score: {metrics_data['action_metrics'].get('communication_score', 'N/A')}</li>
                <li>Leadership Effectiveness Score: {metrics_data['action_metrics'].get('leadership_effectiveness_score', 'N/A')}</li>
                <li>Work-Life Balance Score: {metrics_data['action_metrics'].get('work_life_balance_score', 'N/A')}</li>
                <li>Career Development Score: {metrics_data['action_metrics'].get('career_development_score', 'N/A')}</li>
            </ul>
            <p><a href="/test/{email}">Take Survey Again</a></p>
            """
        else:
            return f"No metrics found for {email}. <a href='/test/{email}'>Take the survey first</a>"

    except Exception as e:
        return f"Error retrieving metrics: {str(e)}"

if __name__ == '__main__':
    app.run(debug=True,port = 8080)

