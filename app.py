"""
Custom Survey Metrics Integration

This module integrates custom survey metrics calculation with the existing Survey system.
It automatically calculates and stores metrics in the existing database tables when
survey responses are submitted.

Usage:
    from app import process_custom_survey_metrics
    process_custom_survey_metrics(email)
"""

import mysql.connector
import pandas as pd
from decimal import Decimal
from custom_survey_metrics import calculate_all_custom_metrics
from existing_table_storage import store_all_metrics_in_existing_tables
from custom_action_calculations import calculate_all_action_metrics

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def process_custom_survey_metrics(email, num_surveys=None):
    """
    Process custom survey metrics for a given email.

    This function:
    1. Retrieves survey responses from student_data table
    2. Calculates all survey and action metrics
    3. Stores results in companyscore and action_table_scores tables

    Args:
        email (str): Email identifier for the survey
        num_surveys (int, optional): Number of surveys sent for response rate calculation

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get survey responses for this email
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        form_url = f"/test/{email}"
        cursor.execute("""
            SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
            FROM student_data
            WHERE form_url = %s
        """, (form_url,))

        survey_data = cursor.fetchall()
        cursor.close()
        conn.close()

        if not survey_data:
            print(f"No survey data found for {email}")
            return False

        # Convert to DataFrame for calculations
        survey_responses = pd.DataFrame(survey_data, columns=[
            'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
        ])

        print(f"Processing metrics for {email}: {survey_responses.shape[0]} responses from {survey_responses['unique_id'].nunique()} unique respondents")

        # Calculate all metrics
        survey_metrics = calculate_all_custom_metrics(email, survey_responses, num_surveys)
        action_metrics = calculate_all_action_metrics(email, survey_responses)

        # Store in existing database tables
        store_all_metrics_in_existing_tables(email, survey_metrics, action_metrics)

        print(f"✅ Metrics successfully calculated and stored for {email}")
        return True

    except Exception as e:
        print(f"❌ Error processing metrics for {email}: {e}")
        return False

# Example usage function
def example_usage():
    """
    Example of how to use the custom survey metrics system.

    Prerequisites:
    1. Questions must be added to employee_questions table for the email
    2. Survey responses must be stored in student_data table
    3. Tables companyscore and action_table_scores must exist
    """
    email = "<EMAIL>"

    # Process metrics for the email
    success = process_custom_survey_metrics(email, num_surveys=10)

    if success:
        print(f"Metrics processed successfully for {email}")
        print("Results are now available in:")
        print("- companyscore table (survey metrics)")
        print("- action_table_scores table (action metrics)")
    else:
        print(f"Failed to process metrics for {email}")

if __name__ == '__main__':
    # Run example
    example_usage()

