from flask import Flask, request, render_template, redirect, session
import mysql.connector
from crewai_agent import predict_sentiment, explain_sentiment
import pandas as pd
from decimal import Decimal
import uuid
from custom_survey_metrics import calculate_all_custom_metrics
from existing_table_storage import store_all_metrics_in_existing_tables
from custom_action_calculations import calculate_all_action_metrics

app = Flask(__name__)
app.secret_key = '********************************************************'

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

# MySQL connection
db = mysql.connector.connect(**DB_CONFIG)
cursor = db.cursor()

@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data
            try:
                for key in request.form:
                    if key.startswith('question_') and not key.startswith('question_text_'):
                        question_number = int(key.split('_')[1])
                        selected_option = request.form[key]

                        # Check if question_text exists in form
                        question_text_key = f'question_text_{question_number}'
                        if question_text_key not in request.form:
                            return f"Error: Missing question text for question {question_number}", 400

                        question_text = request.form[question_text_key]

                        # Combine question and answer for better sentiment analysis
                        combined_text = f"{question_text} {selected_option}"

                        # Perform AI sentiment analysis
                        print(f"Analyzing sentiment for: {combined_text}")
                        predicted_sentiment = predict_sentiment(combined_text)
                        reason = explain_sentiment(combined_text)

                        print(f"Predicted Sentiment: {predicted_sentiment}")

                        cursor.execute("""
                            INSERT INTO student_data (
                                question_number, question_text, selected_text, form_url,
                                unique_id, gender, age_group, tenure_group, role, department, predicted_sentiment, reason
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            question_number, question_text, selected_option, f"/test/{email}",
                            session['unique_id'], session['gender'], session['age_group'],
                            session['tenure_group'], session['role'], session['department'], predicted_sentiment, reason
                        ))

                db.commit()

                # After successful submission, calculate metrics
                try:
                    # Get survey responses for this form_url
                    form_url = f"/test/{email}"
                    cursor.execute("""
                        SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
                        FROM student_data
                        WHERE form_url = %s
                    """, (form_url,))

                    survey_data = cursor.fetchall()
                    if survey_data:
                        # Convert to DataFrame for calculations
                        survey_responses = pd.DataFrame(survey_data, columns=[
                            'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
                        ])

                        # Calculate survey metrics
                        survey_metrics = calculate_all_custom_metrics(email, survey_responses)

                        # Calculate action metrics
                        action_metrics = calculate_all_action_metrics(email, survey_responses)

                        # Store metrics in existing database tables
                        store_all_metrics_in_existing_tables(email, survey_metrics, action_metrics)

                        print(f"Calculated and stored metrics for {email}")
                        print(f"Survey metrics: {survey_metrics['overall']}")
                        print(f"Action categories found: {list(action_metrics['action_metrics'].keys())}")

                except Exception as calc_error:
                    print(f"Error calculating metrics: {calc_error}")
                    # Don't fail the submission if calculations fail

                session.clear()
                return "Thank you! Test submitted successfully."

            except Exception as e:
                db.rollback()
                return f"Error submitting survey: {str(e)}", 500

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)

# Example usage function
def example_usage():
    """
    Example of how to use the custom survey metrics system.

    Prerequisites:
    1. Questions must be added to employee_questions table for the email
    2. Survey responses must be stored in student_data table
    3. Tables companyscore and action_table_scores must exist
    """
    email = "<EMAIL>"

    # Process metrics for the email
    success = process_custom_survey_metrics(email, num_surveys=10)

    if success:
        print(f"Metrics processed successfully for {email}")
        print("Results are now available in:")
        print("- companyscore table (survey metrics)")
        print("- action_table_scores table (action metrics)")
    else:
        print(f"Failed to process metrics for {email}")

if __name__ == '__main__':
    # Run example
    example_usage()

