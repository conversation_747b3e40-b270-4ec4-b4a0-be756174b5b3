from flask import Flask, request, render_template, redirect, session
import mysql.connector
from crewai_agent import predict_sentiment, explain_sentiment
import pandas as pd
from decimal import Decimal
import uuid
from custom_survey_metrics import calculate_all_custom_metrics
from custom_action_calculations import calculate_all_action_metrics

app = Flask(__name__)
app.secret_key = '********************************************************'

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

# MySQL connection
db = mysql.connector.connect(**DB_CONFIG)
cursor = db.cursor()

@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data
            try:
                for key in request.form:
                    if key.startswith('question_') and not key.startswith('question_text_'):
                        question_number = int(key.split('_')[1])
                        selected_option = request.form[key]

                        # Check if question_text exists in form
                        question_text_key = f'question_text_{question_number}'
                        if question_text_key not in request.form:
                            return f"Error: Missing question text for question {question_number}", 400

                        question_text = request.form[question_text_key]

                        # Combine question and answer for better sentiment analysis
                        combined_text = f"{question_text} {selected_option}"

                        # Perform AI sentiment analysis
                        print(f"Analyzing sentiment for: {combined_text}")
                        predicted_sentiment = predict_sentiment(combined_text)
                        reason = explain_sentiment(combined_text)

                        print(f"Predicted Sentiment: {predicted_sentiment}")

                        cursor.execute("""
                            INSERT INTO student_data (
                                question_number, question_text, selected_text, form_url,
                                unique_id, gender, age_group, tenure_group, role, department, predicted_sentiment, reason
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            question_number, question_text, selected_option, f"/test/{email}",
                            session['unique_id'], session['gender'], session['age_group'],
                            session['tenure_group'], session['role'], session['department'], predicted_sentiment, reason
                        ))

                db.commit()

                # After successful submission, calculate metrics
                try:
                    # Get survey responses for this form_url
                    form_url = f"/test/{email}"
                    cursor.execute("""
                        SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
                        FROM student_data
                        WHERE form_url = %s
                    """, (form_url,))

                    survey_data = cursor.fetchall()
                    if survey_data:
                        # Convert to DataFrame for calculations
                        survey_responses = pd.DataFrame(survey_data, columns=[
                            'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
                        ])

                        # Calculate survey metrics
                        survey_metrics = calculate_all_custom_metrics(email, survey_responses)

                        # Calculate action metrics
                        action_metrics = calculate_all_action_metrics(email, survey_responses)

                        # Store metrics directly in database tables
                        try:
                            # Get metrics data
                            overall = survey_metrics['overall']
                            categories = survey_metrics['categories']
                            departments = survey_metrics['departments']
                            experiences = survey_metrics['experiences']
                            genders = survey_metrics['genders']
                            voice_metrics = survey_metrics['voice_metrics']

                            form_url = f"/test/{email}"

                            # First insert/update basic metrics in companyscore
                            cursor.execute('''
                                INSERT INTO companyscore (
                                    survey_url, num_surveys, total_responses,
                                    response_rate, dei_positive_pct, dei_neutral_pct, dei_negative_pct, dei_score
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                                ON DUPLICATE KEY UPDATE
                                num_surveys = VALUES(num_surveys),
                                total_responses = VALUES(total_responses),
                                response_rate = VALUES(response_rate),
                                dei_positive_pct = VALUES(dei_positive_pct),
                                dei_neutral_pct = VALUES(dei_neutral_pct),
                                dei_negative_pct = VALUES(dei_negative_pct),
                                dei_score = VALUES(dei_score)
                            ''', (
                                form_url, overall['num_surveys'], overall['total_responses'],
                                overall['response_rate'], overall['positive_pct'],
                                overall['neutral_pct'], overall['negative_pct'], overall['dei_score']
                            ))

                            # Update DEI metrics
                            cursor.execute('''
                                UPDATE companyscore SET
                                diversity_score = %s, diversity_positive_pct = %s, diversity_neutral_pct = %s, diversity_negative_pct = %s,
                                equity_score = %s, equity_positive_pct = %s, equity_neutral_pct = %s, equity_negative_pct = %s,
                                inclusion_score = %s, inclusion_positive_pct = %s, inclusion_neutral_pct = %s, inclusion_negative_pct = %s
                                WHERE survey_url = %s
                            ''', (
                                overall['diversity_score'], overall['diversity_positive_pct'],
                                overall['diversity_neutral_pct'], overall['diversity_negative_pct'],
                                overall['equity_score'], overall['equity_positive_pct'],
                                overall['equity_neutral_pct'], overall['equity_negative_pct'],
                                overall['inclusion_score'], overall['inclusion_positive_pct'],
                                overall['inclusion_neutral_pct'], overall['inclusion_negative_pct'],
                                form_url
                            ))

                            # Update category metrics
                            for category, metrics in categories.items():
                                if category in ['leadership', 'workplace_satisfaction', 'recognition', 'policies',
                                              'workplace_culture', 'proud_to_work', 'people_care', 'fair_promotion',
                                              'involvement_decision', 'leadership_reachable', 'credibility', 'fairness',
                                              'team_spirit', 'respect', 'open_communication', 'motivation',
                                              'support_motivation', 'strategic_alignment', 'skill_development', 'engagement_rate']:
                                    cursor.execute(f'''
                                        UPDATE companyscore SET
                                        {category}_score = %s,
                                        {category}_positive_pct = %s,
                                        {category}_neutral_pct = %s,
                                        {category}_negative_pct = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['score'], metrics['positive_pct'],
                                        metrics['neutral_pct'], metrics['negative_pct'], form_url
                                    ))
                                elif category == 'wellbeing':
                                    # Map wellbeing to well_being (with underscore)
                                    cursor.execute('''
                                        UPDATE companyscore SET
                                        well_being_score = %s,
                                        well_being_positive_pct = %s,
                                        well_being_neutral_pct = %s,
                                        well_being_negative_pct = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['score'], metrics['positive_pct'],
                                        metrics['neutral_pct'], metrics['negative_pct'], form_url
                                    ))
                                elif category == 'culture_of_engagement':
                                    cursor.execute('''
                                        UPDATE companyscore SET
                                        culture_engagement_score = %s,
                                        culture_engagement_positive_pct = %s,
                                        culture_engagement_neutral_pct = %s,
                                        culture_engagement_negative_pct = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['score'], metrics['positive_pct'],
                                        metrics['neutral_pct'], metrics['negative_pct'], form_url
                                    ))

                            # Update department metrics
                            for dept, metrics in departments.items():
                                column_prefix = f"dept_{dept}_"
                                cursor.execute(f'''
                                    UPDATE companyscore SET
                                    {column_prefix}dei_score = %s,
                                    {column_prefix}positive_pct = %s,
                                    {column_prefix}neutral_pct = %s,
                                    {column_prefix}negative_pct = %s
                                    WHERE survey_url = %s
                                ''', (
                                    metrics['score'], metrics['positive_pct'],
                                    metrics['neutral_pct'], metrics['negative_pct'], form_url
                                ))

                            # Update experience metrics
                            for exp_key, value in experiences.items():
                                # Handle the specific database schema issue for exp_30dto1_negative_pct
                                if exp_key == 'exp_30dto1_negative_pct':
                                    cursor.execute('''
                                        UPDATE companyscore SET
                                        exp_30to1_negative_pct = %s
                                        WHERE survey_url = %s
                                    ''', (value, form_url))
                                else:
                                    cursor.execute(f'''
                                        UPDATE companyscore SET
                                        {exp_key} = %s
                                        WHERE survey_url = %s
                                    ''', (value, form_url))

                            # Update gender metrics
                            for gender_key, value in genders.items():
                                cursor.execute(f'''
                                    UPDATE companyscore SET
                                    {gender_key} = %s
                                    WHERE survey_url = %s
                                ''', (value, form_url))

                            # Update voice metrics
                            for voice_key, metrics_tuple in voice_metrics.items():
                                cursor.execute(f'''
                                    UPDATE companyscore SET
                                    {voice_key}_positive_pct = %s,
                                    {voice_key}_neutral_pct = %s,
                                    {voice_key}_negative_pct = %s,
                                    {voice_key}_score = %s,
                                    {voice_key}_priority = %s
                                    WHERE survey_url = %s
                                ''', (*metrics_tuple, form_url))

                            # Update action metrics
                            action_data = action_metrics['action_metrics']
                            print(f"DEBUG: Action data keys: {list(action_data.keys())}")
                            print(f"DEBUG: Sample action data: {dict(list(action_data.items())[:2])}")

                            # First ensure the record exists
                            cursor.execute('''
                                INSERT IGNORE INTO action_table_scores (survey_url)
                                VALUES (%s)
                            ''', (form_url,))

                            # Update each action category
                            for action_category, metrics in action_data.items():
                                if action_category == 'communication':
                                    cursor.execute('''
                                        UPDATE action_table_scores SET
                                        communication_positive_pct = %s,
                                        communication_neutral_pct = %s,
                                        communication_negative_pct = %s,
                                        communication_score = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['positive_pct'], metrics['neutral_pct'],
                                        metrics['negative_pct'], metrics['score'], form_url
                                    ))
                                elif action_category == 'leadership_effectiveness':
                                    cursor.execute('''
                                        UPDATE action_table_scores SET
                                        leadership_positive_pct = %s,
                                        leadership_neutral_pct = %s,
                                        leadership_negative_pct = %s,
                                        leadership_score = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['positive_pct'], metrics['neutral_pct'],
                                        metrics['negative_pct'], metrics['score'], form_url
                                    ))
                                elif action_category == 'work_life_balance':
                                    cursor.execute('''
                                        UPDATE action_table_scores SET
                                        worklife_balance_positive_pct = %s,
                                        worklife_balance_neutral_pct = %s,
                                        worklife_balance_negative_pct = %s,
                                        worklife_balance_score = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['positive_pct'], metrics['neutral_pct'],
                                        metrics['negative_pct'], metrics['score'], form_url
                                    ))
                                else:
                                    # For all other categories, use the category name directly
                                    cursor.execute(f'''
                                        UPDATE action_table_scores SET
                                        {action_category}_positive_pct = %s,
                                        {action_category}_neutral_pct = %s,
                                        {action_category}_negative_pct = %s,
                                        {action_category}_score = %s
                                        WHERE survey_url = %s
                                    ''', (
                                        metrics['positive_pct'], metrics['neutral_pct'],
                                        metrics['negative_pct'], metrics['score'], form_url
                                    ))

                            db.commit()
                            print(f"Survey metrics stored successfully in companyscore for {email}")
                            print(f"Action metrics stored successfully in action_table_scores for {email}")
                            print(f"All metrics stored successfully in existing tables for {email}")

                        except Exception as e:
                            db.rollback()
                            print(f"Error storing metrics: {e}")

                        print(f"Calculated and stored metrics for {email}")
                        print(f"Survey metrics: {survey_metrics['overall']}")
                        print(f"Action categories found: {list(action_metrics['action_metrics'].keys())}")

                except Exception as calc_error:
                    print(f"Error calculating metrics: {calc_error}")
                    # Don't fail the submission if calculations fail

                session.clear()
                return "Thank you! Test submitted successfully."

            except Exception as e:
                db.rollback()
                return f"Error submitting survey: {str(e)}", 500

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)

if __name__ == '__main__':
    app.run(debug=True, port=8080)

