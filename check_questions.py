"""
Check what questions exist for the email
"""

import mysql.connector

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def check_questions():
    """Check questions and responses"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        email = "<EMAIL>"
        form_url = f"/test/{email}"
        
        print("=== QUESTIONS IN employee_questions TABLE ===")
        cursor.execute("""
            SELECT question_number, question_text
            FROM employee_questions 
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        
        questions = cursor.fetchall()
        for q_num, q_text in questions:
            print(f"Q{q_num}: {q_text}")
        
        print(f"\nTotal questions in employee_questions: {len(questions)}")
        
        print("\n=== RESPONSES IN student_data TABLE ===")
        cursor.execute("""
            SELECT question_number, question_text, selected_text, predicted_sentiment
            FROM student_data 
            WHERE form_url = %s
            ORDER BY question_number
        """, (form_url,))
        
        responses = cursor.fetchall()
        for q_num, q_text, selected, sentiment in responses:
            print(f"Q{q_num}: {q_text[:50]}... -> {selected} ({sentiment})")
        
        print(f"\nTotal responses in student_data: {len(responses)}")
        
        # Check for missing responses
        question_numbers = [q[0] for q in questions]
        response_numbers = [r[0] for r in responses]
        missing = set(question_numbers) - set(response_numbers)
        
        if missing:
            print(f"\nMISSING RESPONSES FOR QUESTIONS: {sorted(missing)}")
        else:
            print("\nAll questions have responses ✅")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_questions()
