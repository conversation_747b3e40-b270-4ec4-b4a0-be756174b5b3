"""
Check what columns exist in companyscore and action_table_scores tables
"""

import mysql.connector

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def check_table_columns():
    """Check what columns exist in the tables"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        print("=== COMPANYSCORE TABLE COLUMNS ===")
        cursor.execute("DESCRIBE companyscore")
        companyscore_columns = cursor.fetchall()
        for column in companyscore_columns:
            print(f"- {column[0]} ({column[1]})")
        
        print("\n=== ACTION_TABLE_SCORES TABLE COLUMNS ===")
        cursor.execute("DESCRIBE action_table_scores")
        action_table_columns = cursor.fetchall()
        for column in action_table_columns:
            print(f"- {column[0]} ({column[1]})")

        print("\n=== STUDENT_DATA TABLE COLUMNS ===")
        cursor.execute("DESCRIBE student_data")
        student_data_columns = cursor.fetchall()
        for column in student_data_columns:
            print(f"- {column[0]} ({column[1]})")
            
        print("\n=== CHECKING FOR SPECIFIC COLUMNS ===")
        
        # Check for specific columns we're trying to use
        test_columns = [
            'communication_score', 'wellbeing_score', 'engagement_score', 
            'culture_engagement_score', 'leadership_score', 'recognition_score'
        ]
        
        companyscore_column_names = [col[0] for col in companyscore_columns]
        
        for col in test_columns:
            exists = col in companyscore_column_names
            print(f"companyscore.{col}: {'✅ EXISTS' if exists else '❌ MISSING'}")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_table_columns()
