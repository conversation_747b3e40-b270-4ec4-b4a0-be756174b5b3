"""
Custom Action Table Metrics Calculator

This module provides dynamic calculation functions for action table metrics based on 
custom questions. It uses AI to categorize questions into action categories and 
calculates various action metrics.
"""

import mysql.connector
import pandas as pd
from decimal import Decimal
from crewai_agent import predict_category

# Action category keywords for dynamic mapping
ACTION_CATEGORY_KEYWORDS = {
    'communication': ['communication', 'speak', 'voice', 'listen', 'feedback', 'discuss', 'talk', 'share'],
    'leadership_effectiveness': ['leadership', 'leader', 'boss', 'manager', 'management', 'supervisor', 'effective'],
    'work_life_balance': ['work-life', 'balance', 'personal life', 'workload', 'stress', 'overtime'],
    'career_development': ['career', 'development', 'growth', 'learn', 'skills', 'training', 'promotion'],
    'recognition_rewards': ['recognition', 'reward', 'appreciate', 'acknowledge', 'valued', 'praise'],
    'employee_engagement': ['engagement', 'involved', 'participate', 'committed', 'motivated', 'enthusiastic'],
    'workplace_environment': ['environment', 'workplace', 'culture', 'atmosphere', 'safe', 'comfortable'],
    'inclusion_diversity': ['inclusion', 'diversity', 'diverse', 'inclusive', 'equal', 'fair', 'background'],
    'compensation_transparency': ['compensation', 'salary', 'pay', 'paid', 'transparent', 'fair pay'],
    'feedback_mechanisms': ['feedback', 'input', 'suggestions', 'opinions', 'concerns', 'listen'],
    'organizational_transparency': ['transparency', 'transparent', 'open', 'honest', 'clear', 'information'],
    'manager_employee_relationship': ['manager', 'relationship', 'boss', 'supervisor', 'support', 'trust'],
    'psychological_safety': ['safe', 'safety', 'comfortable', 'fear', 'speak up', 'psychological'],
    'mission_values_alignment': ['mission', 'values', 'purpose', 'align', 'believe', 'company values'],
    'innovation_creativity': ['innovation', 'creative', 'new ideas', 'innovative', 'creativity', 'think']
}

def get_db_connection():
    """Create and return a database connection"""
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

def get_questions_for_email(email):
    """Get all questions for a specific email from employee_questions table"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT question_number, question_text
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))

        questions = cursor.fetchall()
        # Add None for category since it doesn't exist in the table
        return [(q[0], q[1], None) for q in questions]
    finally:
        cursor.close()
        conn.close()

def categorize_action_question(question_text):
    """Use AI and keywords to categorize a question into action categories"""
    try:
        # Use AI to predict categories
        categories = predict_category(question_text)
        
        # Parse the result
        if isinstance(categories, str):
            categories = [cat.strip().lower() for cat in categories.split(',')]
        
        # Map AI categories to our action system
        mapped_categories = []
        question_lower = question_text.lower()
        
        for category in categories:
            for our_cat, keywords in ACTION_CATEGORY_KEYWORDS.items():
                if any(keyword in category or keyword in question_lower for keyword in keywords):
                    if our_cat not in mapped_categories:
                        mapped_categories.append(our_cat)
        
        # Fallback to keyword matching if AI doesn't match
        if not mapped_categories:
            for action_cat, keywords in ACTION_CATEGORY_KEYWORDS.items():
                if any(keyword in question_lower for keyword in keywords):
                    mapped_categories.append(action_cat)
        
        return mapped_categories if mapped_categories else ['general']
        
    except Exception as e:
        print(f"Error categorizing action question: {e}")
        # Fallback to keyword matching
        question_lower = question_text.lower()
        for action_cat, keywords in ACTION_CATEGORY_KEYWORDS.items():
            if any(keyword in question_lower for keyword in keywords):
                return [action_cat]
        return ['general']

def get_dynamic_action_mapping(email):
    """Create dynamic action question mapping based on questions for specific email"""
    questions = get_questions_for_email(email)
    
    action_mapping = {
        'communication': [],
        'leadership_effectiveness': [],
        'work_life_balance': [],
        'career_development': [],
        'recognition_rewards': [],
        'employee_engagement': [],
        'workplace_environment': [],
        'inclusion_diversity': [],
        'compensation_transparency': [],
        'feedback_mechanisms': [],
        'organizational_transparency': [],
        'manager_employee_relationship': [],
        'psychological_safety': [],
        'mission_values_alignment': [],
        'innovation_creativity': [],
        'general': []
    }
    
    for question_number, question_text, stored_category in questions:
        # Use stored category if available, otherwise categorize dynamically
        if stored_category and stored_category.lower() in action_mapping:
            categories = [stored_category.lower()]
        else:
            categories = categorize_action_question(question_text)
        
        # Add to action mappings
        for category in categories:
            if category in action_mapping:
                action_mapping[category].append(question_number)
    
    return action_mapping

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0]

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    if not question_numbers:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')
    
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)

def calculate_action_score(positive_pct, neutral_pct, negative_pct):
    """Calculate action score based on sentiment percentages using same weights as DEI score."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

def calculate_action_category_metrics(survey_responses, question_numbers):
    """Calculate metrics for a specific action category."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_all_action_metrics(email, survey_responses):
    """Calculate metrics for all action categories based on custom questions."""
    # Get dynamic action mappings
    action_mapping = get_dynamic_action_mapping(email)
    
    # Calculate metrics for each action category
    action_metrics = {}
    
    for action_category, question_numbers in action_mapping.items():
        if action_category != 'general':  # Skip general category for action metrics
            action_metrics[action_category] = calculate_action_category_metrics(
                survey_responses, question_numbers
            )
    
    return {
        'action_metrics': action_metrics,
        'action_mapping': action_mapping
    }
