"""
Custom Metrics Storage

This module handles storing calculated metrics in the database for custom surveys.
"""

import mysql.connector
from decimal import Decimal

def get_db_connection():
    """Create and return a database connection"""
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

def store_survey_metrics(email, form_url, survey_metrics):
    """Store survey metrics in custom_survey_scores table"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        overall = survey_metrics['overall']
        categories = survey_metrics['categories']
        departments = survey_metrics['departments']
        experiences = survey_metrics['experiences']
        genders = survey_metrics['genders']
        roles = survey_metrics['roles']
        voices = survey_metrics['voices']
        
        # Insert/Update basic metrics
        cursor.execute('''
            INSERT INTO custom_survey_scores (
                email, form_url, total_responses, response_rate, 
                dei_positive_pct, dei_neutral_pct, dei_negative_pct, dei_score
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                total_responses = VALUES(total_responses),
                response_rate = VALUES(response_rate),
                dei_positive_pct = VALUES(dei_positive_pct),
                dei_neutral_pct = VALUES(dei_neutral_pct),
                dei_negative_pct = VALUES(dei_negative_pct),
                dei_score = VALUES(dei_score)
        ''', (
            email, form_url, overall['total_responses'], overall['response_rate'],
            overall['positive_pct'], overall['neutral_pct'], overall['negative_pct'], overall['dei_score']
        ))
        
        # Update category metrics
        category_updates = []
        category_values = []
        
        for category, metrics in categories.items():
            if category in ['diversity', 'equity', 'inclusion', 'engagement', 'leadership', 
                          'communication', 'workplace_satisfaction', 'wellbeing', 'recognition', 
                          'career_development', 'policies']:
                category_updates.extend([
                    f"{category}_score = %s",
                    f"{category}_positive_pct = %s", 
                    f"{category}_neutral_pct = %s",
                    f"{category}_negative_pct = %s"
                ])
                category_values.extend([
                    metrics['score'], metrics['positive_pct'], 
                    metrics['neutral_pct'], metrics['negative_pct']
                ])
        
        if category_updates:
            update_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(category_updates)}
                WHERE email = %s AND form_url = %s
            '''
            category_values.extend([email, form_url])
            cursor.execute(update_query, category_values)
        
        # Update department metrics
        dept_updates = []
        dept_values = []
        
        for dept_key, value in departments.items():
            dept_updates.append(f"{dept_key} = %s")
            dept_values.append(value)
        
        if dept_updates:
            dept_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(dept_updates)}
                WHERE email = %s AND form_url = %s
            '''
            dept_values.extend([email, form_url])
            cursor.execute(dept_query, dept_values)

        # Update experience metrics
        exp_updates = []
        exp_values = []

        for exp_key, value in experiences.items():
            exp_updates.append(f"{exp_key} = %s")
            exp_values.append(value)

        if exp_updates:
            exp_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(exp_updates)}
                WHERE email = %s AND form_url = %s
            '''
            exp_values.extend([email, form_url])
            cursor.execute(exp_query, exp_values)

        # Update gender metrics
        gender_updates = []
        gender_values = []

        for gender_key, value in genders.items():
            gender_updates.append(f"{gender_key} = %s")
            gender_values.append(value)

        if gender_updates:
            gender_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(gender_updates)}
                WHERE email = %s AND form_url = %s
            '''
            gender_values.extend([email, form_url])
            cursor.execute(gender_query, gender_values)

        # Update role metrics
        role_updates = []
        role_values = []

        for role_key, value in roles.items():
            role_updates.append(f"{role_key} = %s")
            role_values.append(value)

        if role_updates:
            role_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(role_updates)}
                WHERE email = %s AND form_url = %s
            '''
            role_values.extend([email, form_url])
            cursor.execute(role_query, role_values)

        # Update voice metrics
        voice_updates = []
        voice_values = []

        for voice_key, voice_metrics in voices.items():
            # voice_metrics is a tuple: (positive_pct, neutral_pct, negative_pct, score, priority)
            voice_num = voice_key.replace('voice', '')
            voice_updates.extend([
                f"emp_voice{voice_num}_positive_pct = %s",
                f"emp_voice{voice_num}_neutral_pct = %s",
                f"emp_voice{voice_num}_negative_pct = %s",
                f"emp_voice{voice_num}_score = %s",
                f"emp_voice{voice_num}_priority = %s"
            ])
            voice_values.extend([
                voice_metrics[0], voice_metrics[1], voice_metrics[2],
                voice_metrics[3], voice_metrics[4]
            ])

        if voice_updates:
            voice_query = f'''
                UPDATE custom_survey_scores SET
                {', '.join(voice_updates)}
                WHERE email = %s AND form_url = %s
            '''
            voice_values.extend([email, form_url])
            cursor.execute(voice_query, voice_values)

        conn.commit()
        print(f"Survey metrics stored successfully for {email}")
        
    except Exception as e:
        print(f"Error storing survey metrics: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def store_action_metrics(email, form_url, action_metrics):
    """Store action metrics in custom_action_scores table"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        action_data = action_metrics['action_metrics']
        
        # Insert/Update basic record
        cursor.execute('''
            INSERT INTO custom_action_scores (email, form_url)
            VALUES (%s, %s)
            ON DUPLICATE KEY UPDATE email = VALUES(email), form_url = VALUES(form_url)
        ''', (email, form_url))
        
        # Update action category metrics
        action_updates = []
        action_values = []
        
        for action_category, metrics in action_data.items():
            # Map category names to database column names
            db_category = action_category
            if action_category == 'leadership_effectiveness':
                db_category = 'leadership_effectiveness'
            elif action_category == 'work_life_balance':
                db_category = 'work_life_balance'
            elif action_category == 'career_development':
                db_category = 'career_development'
            elif action_category == 'recognition_rewards':
                db_category = 'recognition_rewards'
            elif action_category == 'employee_engagement':
                db_category = 'employee_engagement'
            elif action_category == 'workplace_environment':
                db_category = 'workplace_environment'
            elif action_category == 'inclusion_diversity':
                db_category = 'inclusion_diversity'
            elif action_category == 'compensation_transparency':
                db_category = 'compensation_transparency'
            elif action_category == 'feedback_mechanisms':
                db_category = 'feedback_mechanisms'
            elif action_category == 'organizational_transparency':
                db_category = 'organizational_transparency'
            elif action_category == 'manager_employee_relationship':
                db_category = 'manager_employee_relationship'
            elif action_category == 'psychological_safety':
                db_category = 'psychological_safety'
            elif action_category == 'mission_values_alignment':
                db_category = 'mission_values_alignment'
            elif action_category == 'innovation_creativity':
                db_category = 'innovation_creativity'
            
            action_updates.extend([
                f"{db_category}_positive_pct = %s",
                f"{db_category}_neutral_pct = %s", 
                f"{db_category}_negative_pct = %s",
                f"{db_category}_score = %s"
            ])
            action_values.extend([
                metrics['positive_pct'], metrics['neutral_pct'], 
                metrics['negative_pct'], metrics['score']
            ])
        
        if action_updates:
            update_query = f'''
                UPDATE custom_action_scores SET
                {', '.join(action_updates)}
                WHERE email = %s AND form_url = %s
            '''
            action_values.extend([email, form_url])
            cursor.execute(update_query, action_values)
        
        conn.commit()
        print(f"Action metrics stored successfully for {email}")
        
    except Exception as e:
        print(f"Error storing action metrics: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def store_all_custom_metrics(email, form_url, survey_metrics, action_metrics):
    """Store both survey and action metrics"""
    try:
        store_survey_metrics(email, form_url, survey_metrics)
        store_action_metrics(email, form_url, action_metrics)
        print(f"All custom metrics stored successfully for {email}")
    except Exception as e:
        print(f"Error storing all custom metrics: {e}")
