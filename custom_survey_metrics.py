"""
Custom Survey Metrics Calculator

This module provides dynamic calculation functions for survey metrics based on 
custom questions stored in the employee_questions table. It dynamically determines
question categories using AI and calculates various metrics including:
- DEI (Diversity, Equity, Inclusion) scores
- Department-wise metrics  
- Experience-based metrics
- Role engagement metrics
- Employee voice metrics
"""

import mysql.connector
import pandas as pd
from decimal import Decimal
from crewai_agent import predict_category, predict_voice_statement

# Constants for mapping form values to database fields
DEPARTMENT_MAPPING = {
    'HR': 'HR_and_Admin',
    'Finance': 'Finance_and_accounting', 
    'Sales': 'Sales_marketing',
    'Product': 'Product_development',
    'Technical': 'Technical',
    'Operations': 'Operations',
    'Procurements': 'Procurement',
    'Quality': 'Quality',
    'Business': 'Business_Development',
    'Executive': 'executive',
    'Leadership': 'leadership',
    'Management': 'Management',
    'Others': 'Others'
}

EXPERIENCE_MAPPING = {
    '0-1 year': '30dto1',
    '1-3 years': '1to3', 
    '3-5 years': '3to5',
    '5+ years': 'above5',
}

ROLE_MAPPING = {
    'Junior Staff': 'Junior_staff',
    'Senior Staff': 'Senior_staff',
    'Manager': 'manager',
    'Executive': 'executive',
    'Trainee': 'trainee',
    'Team': 'team_member'
}

GENDER_MAPPING = {
    'Male': 'male',
    'Female': 'female',
    'Other': 'others'
}

# Category keywords for dynamic mapping
CATEGORY_KEYWORDS = {
    'diversity': ['diversity', 'hire', 'promote', 'equal chance', 'background', 'fair hiring'],
    'equity': ['equity', 'fair', 'equal treatment', 'boss support', 'paid', 'compensation', 'salary'],
    'inclusion': ['inclusion', 'valued', 'appreciated', 'trust', 'included', 'belonging', 'voice'],
    'engagement': ['engagement', 'culture', 'valued', 'appreciated', 'trust', 'involved'],
    'leadership': ['leadership', 'boss', 'manager', 'management', 'leader', 'supervisor'],
    'communication': ['communication', 'voice', 'speak', 'listen', 'feedback', 'discuss'],
    'workplace_satisfaction': ['satisfaction', 'happy', 'comfortable', 'environment', 'workplace'],
    'wellbeing': ['wellbeing', 'work-life balance', 'stress', 'health', 'mental health'],
    'recognition': ['recognition', 'appreciated', 'valued', 'reward', 'acknowledge'],
    'career_development': ['career', 'development', 'growth', 'learn', 'skills', 'training'],
    'policies': ['policies', 'support', 'procedures', 'rules', 'guidelines']
}

def get_db_connection():
    """Create and return a database connection"""
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

def get_questions_for_email(email):
    """Get all questions for a specific email from employee_questions table"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT question_number, question_text
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))

        questions = cursor.fetchall()
        # Add None for category since it doesn't exist in the table
        return [(q[0], q[1], None) for q in questions]
    finally:
        cursor.close()
        conn.close()

def categorize_question_dynamically(question_text):
    """Use AI to categorize a question into predefined categories"""
    try:
        # Use AI to predict categories
        categories = predict_category(question_text)
        
        # Parse the result and map to our category system
        if isinstance(categories, str):
            categories = [cat.strip().lower() for cat in categories.split(',')]
        
        # Map AI categories to our system
        mapped_categories = []
        for category in categories:
            for our_cat, keywords in CATEGORY_KEYWORDS.items():
                if any(keyword in category or keyword in question_text.lower() for keyword in keywords):
                    if our_cat not in mapped_categories:
                        mapped_categories.append(our_cat)
        
        return mapped_categories if mapped_categories else ['general']
        
    except Exception as e:
        print(f"Error categorizing question: {e}")
        # Fallback to keyword matching
        question_lower = question_text.lower()
        for category, keywords in CATEGORY_KEYWORDS.items():
            if any(keyword in question_lower for keyword in keywords):
                return [category]
        return ['general']

def get_dynamic_question_mapping(email):
    """Create dynamic question mapping based on questions for specific email"""
    questions = get_questions_for_email(email)
    
    category_mapping = {
        'diversity': [],
        'equity': [],
        'inclusion': [],
        'engagement': [],
        'leadership': [],
        'communication': [],
        'workplace_satisfaction': [],
        'wellbeing': [],
        'recognition': [],
        'career_development': [],
        'policies': [],
        'general': []
    }
    
    voice_mapping = {}
    
    for question_number, question_text, stored_category in questions:
        # Use stored category if available, otherwise categorize dynamically
        if stored_category:
            categories = [stored_category.lower()]
        else:
            categories = categorize_question_dynamically(question_text)
        
        # Add to category mappings
        for category in categories:
            if category in category_mapping:
                category_mapping[category].append(question_number)
        
        # Get voice statement mapping
        try:
            voice_num = predict_voice_statement(question_text)
            if voice_num > 0:
                if voice_num not in voice_mapping:
                    voice_mapping[voice_num] = []
                voice_mapping[voice_num].append(question_number)
        except Exception as e:
            print(f"Error mapping voice statement for question {question_number}: {e}")
    
    return category_mapping, voice_mapping

# Core calculation functions
def calculate_total_responses(survey_responses):
    """Calculate the total number of unique employees who responded to the survey."""
    return survey_responses['unique_id'].nunique()

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')
    
    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0]

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    if not question_numbers:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')
    
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)

def calculate_dei_score(positive_pct, neutral_pct, negative_pct):
    """Calculate DEI score based on sentiment percentages."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

# Department metrics
def calculate_department_metrics(survey_responses):
    """Calculate DEI metrics for each department."""
    metrics = {}

    for dept_form_value, dept_db_value in DEPARTMENT_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['department'] == dept_form_value]

        if filtered_responses.empty:
            metrics[f'dept_{dept_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'dept_{dept_db_value}_dei_score'] = dei_score
        metrics[f'dept_{dept_db_value}_positive_pct'] = positive_pct
        metrics[f'dept_{dept_db_value}_neutral_pct'] = neutral_pct
        metrics[f'dept_{dept_db_value}_negative_pct'] = negative_pct

    return metrics

# Experience metrics
def calculate_experience_metrics(survey_responses):
    """Calculate DEI metrics for different experience levels."""
    metrics = {}

    for exp_form_value, exp_db_value in EXPERIENCE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['tenure_group'] == exp_form_value]

        if filtered_responses.empty:
            metrics[f'exp_{exp_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'exp_{exp_db_value}_dei_score'] = dei_score
        metrics[f'exp_{exp_db_value}_positive_pct'] = positive_pct
        metrics[f'exp_{exp_db_value}_neutral_pct'] = neutral_pct
        metrics[f'exp_{exp_db_value}_negative_pct'] = negative_pct

    return metrics

# Role engagement metrics
def calculate_role_engagement_metrics(survey_responses, engagement_questions):
    """Calculate engagement metrics for different roles."""
    metrics = {}

    for role_form_value, role_db_value in ROLE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['role'] == role_form_value]

        if filtered_responses.empty:
            metrics[f'role_{role_db_value}_engagement_score'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, engagement_questions
        )
        engagement_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'role_{role_db_value}_engagement_score'] = engagement_score
        metrics[f'role_{role_db_value}_positive_pct'] = positive_pct
        metrics[f'role_{role_db_value}_neutral_pct'] = neutral_pct
        metrics[f'role_{role_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_gender_metrics(survey_responses):
    """Calculate DEI metrics for different gender categories."""
    metrics = {}

    for gender_form_value, gender_db_value in GENDER_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'gender_{gender_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'gender_{gender_db_value}_dei_score'] = dei_score
        metrics[f'gender_{gender_db_value}_positive_pct'] = positive_pct
        metrics[f'gender_{gender_db_value}_neutral_pct'] = neutral_pct
        metrics[f'gender_{gender_db_value}_negative_pct'] = negative_pct

    return metrics

def prioritize_voice_statements(scores):
    """Prioritize voice statements based on their scores (lowest scores get highest priority)."""
    sorted_statements = sorted(scores.items(), key=lambda x: x[1])
    priorities = {}
    for i, (statement_num, _) in enumerate(sorted_statements, 1):
        priorities[statement_num] = i
    return priorities

def calculate_voice_metrics(survey_responses, voice_mapping):
    """Calculate all employee voice metrics with priorities."""
    voice_metrics = {}
    scores = {}

    for statement_num in range(1, 14):  # Voice statements 1-13
        question_list = voice_mapping.get(statement_num, [])

        if not question_list:
            metrics = (Decimal('0.00'), Decimal('0.00'), Decimal('0.00'), Decimal('0.00'))
            voice_metrics[f'voice{statement_num}'] = metrics
            scores[statement_num] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_list
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics = (positive_pct, neutral_pct, negative_pct, score)
        voice_metrics[f'voice{statement_num}'] = metrics
        scores[statement_num] = score

    # Calculate priorities based on scores (lowest score = highest priority)
    priorities = prioritize_voice_statements(scores)

    # Return in format: (positive_pct, neutral_pct, negative_pct, score, priority)
    return {
        key: (*metrics, priorities[int(key[5:])])
        for key, metrics in voice_metrics.items()
    }

def calculate_category_metrics(survey_responses, category_mapping):
    """Calculate metrics for all question categories."""
    metrics = {}

    for category, question_numbers in category_mapping.items():
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[category] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    return metrics

def calculate_all_custom_metrics(email, survey_responses, num_surveys=None):
    """Calculate all metrics for custom survey questions."""
    # Get dynamic question mappings
    category_mapping, voice_mapping = get_dynamic_question_mapping(email)

    # Calculate basic metrics
    total_responses = calculate_total_responses(survey_responses)
    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
    dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    response_rate = Decimal('0.00')
    if num_surveys:
        response_rate = calculate_response_rate(total_responses, num_surveys)

    # Calculate all category metrics
    category_metrics = calculate_category_metrics(survey_responses, category_mapping)

    # Calculate demographic metrics
    department_metrics = calculate_department_metrics(survey_responses)
    experience_metrics = calculate_experience_metrics(survey_responses)
    gender_metrics = calculate_gender_metrics(survey_responses)

    # Calculate role metrics using engagement questions
    engagement_questions = category_mapping.get('engagement', [])
    role_metrics = calculate_role_engagement_metrics(survey_responses, engagement_questions)

    # Calculate voice metrics
    voice_metrics = calculate_voice_metrics(survey_responses, voice_mapping)

    return {
        'overall': {
            'total_responses': total_responses,
            'response_rate': response_rate,
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'dei_score': dei_score
        },
        'categories': category_metrics,
        'departments': department_metrics,
        'experiences': experience_metrics,
        'genders': gender_metrics,
        'roles': role_metrics,
        'voices': voice_metrics,
        'category_mapping': category_mapping,
        'voice_mapping': voice_mapping
    }
