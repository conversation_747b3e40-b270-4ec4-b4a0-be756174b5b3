"""
Custom Survey Metrics Calculator

This module provides dynamic calculation functions for survey metrics based on 
custom questions stored in the employee_questions table. It dynamically determines
question categories using AI and calculates various metrics including:
- DEI (Diversity, Equity, Inclusion) scores
- Department-wise metrics  
- Experience-based metrics
- Role engagement metrics
- Employee voice metrics
"""

import mysql.connector
import pandas as pd
from decimal import Decimal
from crewai_agent import predict_category, predict_voice_statement

# Constants for mapping form values to database fields
DEPARTMENT_MAPPING = {
    'HR': 'HR_and_Admin',
    'Finance': 'Finance_and_accounting', 
    'Sales': 'Sales_marketing',
    'Product': 'Product_development',
    'Technical': 'Technical',
    'Operations': 'Operations',
    'Procurements': 'Procurement',
    'Quality': 'Quality',
    'Business': 'Business_Development',
    'Executive': 'executive',
    'Leadership': 'leadership',
    'Management': 'Management',
    'Others': 'Others'
}

EXPERIENCE_MAPPING = {
    '0-1 year': '30dto1',
    '1-3 years': '1to3', 
    '3-5 years': '3to5',
    '5+ years': 'above5',
}

ROLE_MAPPING = {
    'Junior Staff': 'Junior_staff',
    'Senior Staff': 'Senior_staff',
    'Manager': 'manager',
    'Executive': 'executive',
    'Trainee': 'trainee',
    'Team': 'team_member'
}

GENDER_MAPPING = {
    'Male': 'male',
    'Female': 'female',
    'Other': 'others'
}

# Comprehensive category examples for AI-based classification
CATEGORY_EXAMPLES = {
    'diversity': "Questions about hiring people from different backgrounds, promoting diversity, equal opportunities for all demographics, representation, inclusive practices",
    'equity': "Questions about fair treatment, equal pay, compensation equity, fair distribution of resources, equal access to opportunities",
    'inclusion': "Questions about feeling valued, belonging, being included in discussions, feeling welcomed, having your voice heard",
    'culture_of_engagement': "Questions about employee involvement, participation in company culture, feeling engaged with work and colleagues",
    'support_motivation': "Questions about receiving support from managers, feeling motivated, encouragement, help with goals and development",
    'strategic_alignment': "Questions about understanding company vision/mission, alignment with organizational goals, clarity of direction",
    'skill_development': "Questions about training opportunities, learning new skills, professional development, competency building",
    'engagement_rate': "Questions about active participation, involvement in initiatives, commitment to work and organization",
    'leadership': "Questions about trust in leaders, management effectiveness, supervisor quality, executive performance",
    'credibility': "Questions about trust, reliability, honesty, integrity, believability of leadership and organization",
    'fairness': "Questions about fair treatment, just decisions, equitable policies, impartial processes",
    'team_spirit': "Questions about collaboration, teamwork, unity, cooperation, working together effectively",
    'wellbeing': "Questions about work-life balance, stress levels, mental health, physical wellness, overall wellbeing",
    'respect': "Questions about being treated with dignity, courtesy, consideration, respectful interactions",
    'workplace_satisfaction': "Questions about happiness at work, satisfaction with environment, comfort in workplace",
    'open_communication': "Questions about transparent communication, honest dialogue, clear information sharing",
    'recognition': "Questions about feeling appreciated, valued, acknowledged, rewarded for contributions",
    'motivation': "Questions about feeling inspired, driven, encouraged, motivated to perform well",
    'workplace_culture': "Questions about organizational atmosphere, cultural values, work environment, company culture",
    'proud_to_work': "Questions about pride in working for the organization, satisfaction with employer, happiness about job",
    'people_care': "Questions about whether the company cares about employees, shows concern for welfare, demonstrates empathy",
    'fair_promotion': "Questions about fair advancement opportunities, equitable promotion processes, career growth fairness",
    'involvement_decision': "Questions about being consulted on decisions, having input, participating in choices that affect work",
    'leadership_reachable': "Questions about accessibility of leaders, approachable management, open-door policies",
    'communication': "Questions about effectiveness of communication, information flow, feedback mechanisms",
    'career_development': "Questions about career growth opportunities, professional development, advancement paths",
    'policies': "Questions about organizational policies, procedures, guidelines, rules and their effectiveness"
}

def get_db_connection():
    """Create and return a database connection"""
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

def get_questions_for_email(email):
    """Get all questions for a specific email from employee_questions table"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("""
            SELECT question_number, question_text
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))

        questions = cursor.fetchall()
        # Add None for category since it doesn't exist in the table
        return [(q[0], q[1], None) for q in questions]
    finally:
        cursor.close()
        conn.close()

def categorize_question_dynamically(question_text):
    """Use AI to categorize a question into predefined categories with comprehensive examples"""
    try:
        # Create a comprehensive prompt with examples for better accuracy
        examples_text = "\n".join([f"- {cat}: {desc}" for cat, desc in CATEGORY_EXAMPLES.items()])

        category_prompt = f"""
# Agent: Category Classifier
## Task:
You are an expert in workplace sentiment analysis. Analyze the following survey question and determine which workplace categories it relates to.

## Available Categories with Descriptions:
{examples_text}

## Question to Analyze:
"{question_text}"

## Instructions:
1. Identify ALL relevant categories that this question relates to (can be multiple)
2. Consider both direct and indirect relationships
3. Look for semantic meaning, not just keywords
4. Return categories as a comma-separated list using exact category names
5. If no clear match, return 'general'
6. Be comprehensive but accurate

## Response Format:
Just return the category names separated by commas, nothing else.
"""

        # Use existing predict_category function
        result = predict_category(category_prompt)
        categories = [cat.strip().lower() for cat in result.split(',')]

        # Validate categories against our known categories
        valid_categories = []
        for category in categories:
            if category in CATEGORY_EXAMPLES:
                valid_categories.append(category)
            else:
                # Try to find partial matches
                for known_cat in CATEGORY_EXAMPLES.keys():
                    if category in known_cat or known_cat in category:
                        if known_cat not in valid_categories:
                            valid_categories.append(known_cat)

        return valid_categories if valid_categories else ['general']

    except Exception as e:
        print(f"Error categorizing question: {e}")
        # Fallback to simple keyword matching
        question_lower = question_text.lower()
        fallback_keywords = {
            'diversity': ['diversity', 'hire', 'background'],
            'equity': ['fair', 'equal', 'compensation'],
            'inclusion': ['valued', 'included', 'belonging'],
            'leadership': ['manager', 'boss', 'leader'],
            'recognition': ['appreciated', 'valued', 'recognized'],
            'communication': ['communication', 'feedback'],
            'career_development': ['career', 'development', 'growth']
        }

        for category, keywords in fallback_keywords.items():
            if any(keyword in question_lower for keyword in keywords):
                return [category]
        return ['general']

def get_dynamic_question_mapping(email):
    """Create dynamic question mapping based on questions for specific email"""
    questions = get_questions_for_email(email)
    
    category_mapping = {
        'diversity': [],
        'equity': [],
        'inclusion': [],
        'engagement': [],
        'culture_of_engagement': [],
        'support_motivation': [],
        'strategic_alignment': [],
        'skill_development': [],
        'engagement_rate': [],
        'leadership': [],
        'credibility': [],
        'fairness': [],
        'team_spirit': [],
        'wellbeing': [],
        'respect': [],
        'workplace_satisfaction': [],
        'open_communication': [],
        'recognition': [],
        'motivation': [],
        'workplace_culture': [],
        'proud_to_work': [],
        'people_care': [],
        'fair_promotion': [],
        'involvement_decision': [],
        'leadership_reachable': [],
        'communication': [],
        'career_development': [],
        'policies': [],
        'general': []
    }
    
    voice_mapping = {}
    
    for question_number, question_text, stored_category in questions:
        # Use stored category if available, otherwise categorize dynamically
        if stored_category:
            categories = [stored_category.lower()]
        else:
            categories = categorize_question_dynamically(question_text)
        
        # Add to category mappings
        for category in categories:
            if category in category_mapping:
                category_mapping[category].append(question_number)
        
        # Get voice statement mapping
        try:
            voice_num = predict_voice_statement(question_text)
            if voice_num > 0:
                if voice_num not in voice_mapping:
                    voice_mapping[voice_num] = []
                voice_mapping[voice_num].append(question_number)
        except Exception as e:
            print(f"Error mapping voice statement for question {question_number}: {e}")
    
    return category_mapping, voice_mapping

# Core calculation functions
def calculate_total_responses(survey_responses):
    """Calculate the total number of unique employees who responded to the survey."""
    return survey_responses['unique_id'].nunique()

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')

    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')
    
    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0]

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    if not question_numbers:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')
    
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)

def calculate_dei_score(positive_pct, neutral_pct, negative_pct):
    """Calculate DEI score based on sentiment percentages."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

# Department metrics
def calculate_department_metrics(survey_responses):
    """Calculate DEI metrics for each department."""
    metrics = {}

    for dept_form_value, dept_db_value in DEPARTMENT_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['department'] == dept_form_value]

        if filtered_responses.empty:
            metrics[f'dept_{dept_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'dept_{dept_db_value}_dei_score'] = dei_score
        metrics[f'dept_{dept_db_value}_positive_pct'] = positive_pct
        metrics[f'dept_{dept_db_value}_neutral_pct'] = neutral_pct
        metrics[f'dept_{dept_db_value}_negative_pct'] = negative_pct

    return metrics

# Experience metrics
def calculate_experience_metrics(survey_responses):
    """Calculate DEI metrics for different experience levels."""
    metrics = {}

    for exp_form_value, exp_db_value in EXPERIENCE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['tenure_group'] == exp_form_value]

        if filtered_responses.empty:
            metrics[f'exp_{exp_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'exp_{exp_db_value}_dei_score'] = dei_score
        metrics[f'exp_{exp_db_value}_positive_pct'] = positive_pct
        metrics[f'exp_{exp_db_value}_neutral_pct'] = neutral_pct
        metrics[f'exp_{exp_db_value}_negative_pct'] = negative_pct

    return metrics

# Role engagement metrics
def calculate_role_engagement_metrics(survey_responses, engagement_questions):
    """Calculate engagement metrics for different roles."""
    metrics = {}

    for role_form_value, role_db_value in ROLE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['role'] == role_form_value]

        if filtered_responses.empty:
            metrics[f'role_{role_db_value}_engagement_score'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, engagement_questions
        )
        engagement_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'role_{role_db_value}_engagement_score'] = engagement_score
        metrics[f'role_{role_db_value}_positive_pct'] = positive_pct
        metrics[f'role_{role_db_value}_neutral_pct'] = neutral_pct
        metrics[f'role_{role_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_gender_metrics(survey_responses):
    """Calculate DEI metrics for different gender categories."""
    metrics = {}

    for gender_form_value, gender_db_value in GENDER_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'gender_{gender_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'gender_{gender_db_value}_dei_score'] = dei_score
        metrics[f'gender_{gender_db_value}_positive_pct'] = positive_pct
        metrics[f'gender_{gender_db_value}_neutral_pct'] = neutral_pct
        metrics[f'gender_{gender_db_value}_negative_pct'] = negative_pct

    return metrics

# Individual category calculation functions
def calculate_culture_of_engagement_metrics(survey_responses, question_numbers):
    """Calculate culture of engagement metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_support_motivation_metrics(survey_responses, question_numbers):
    """Calculate support and motivation metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_strategic_alignment_metrics(survey_responses, question_numbers):
    """Calculate strategic alignment metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_skill_development_metrics(survey_responses, question_numbers):
    """Calculate skill development metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_engagement_rate_metrics(survey_responses, question_numbers):
    """Calculate engagement rate metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_credibility_metrics(survey_responses, question_numbers):
    """Calculate credibility metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_fairness_metrics(survey_responses, question_numbers):
    """Calculate fairness metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_team_spirit_metrics(survey_responses, question_numbers):
    """Calculate team spirit metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_respect_metrics(survey_responses, question_numbers):
    """Calculate respect metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_open_communication_metrics(survey_responses, question_numbers):
    """Calculate open communication metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_motivation_metrics(survey_responses, question_numbers):
    """Calculate motivation metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_workplace_culture_metrics(survey_responses, question_numbers):
    """Calculate workplace culture metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_proud_to_work_metrics(survey_responses, question_numbers):
    """Calculate proud to work metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_people_care_metrics(survey_responses, question_numbers):
    """Calculate people care metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_fair_promotion_metrics(survey_responses, question_numbers):
    """Calculate fair promotion metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_involvement_decision_metrics(survey_responses, question_numbers):
    """Calculate involvement in decision metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_reachable_metrics(survey_responses, question_numbers):
    """Calculate leadership reachable metrics."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def prioritize_voice_statements(scores):
    """Prioritize voice statements based on their scores (higher scores get higher priority)."""
    sorted_statements = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    priorities = {}
    for i, (statement_num, _) in enumerate(sorted_statements, 1):
        priorities[statement_num] = i
    return priorities

def calculate_voice_metrics(survey_responses, voice_mapping):
    """Calculate all employee voice metrics with priorities."""
    voice_metrics = {}
    scores = {}

    for statement_num in range(1, 14):  # Voice statements 1-13
        question_list = voice_mapping.get(statement_num, [])

        if not question_list:
            metrics = (Decimal('0.00'), Decimal('0.00'), Decimal('0.00'), Decimal('0.00'))
            voice_metrics[f'voice{statement_num}'] = metrics
            scores[statement_num] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_list
        )
        # Voice score = neutral + negative (higher score = higher priority for action)
        score = neutral_pct + negative_pct

        metrics = (positive_pct, neutral_pct, negative_pct, score)
        voice_metrics[f'voice{statement_num}'] = metrics
        scores[statement_num] = score

    # Calculate priorities based on scores (higher score = higher priority)
    priorities = prioritize_voice_statements(scores)

    # Return in format: (positive_pct, neutral_pct, negative_pct, score, priority)
    return {
        key: (*metrics, priorities[int(key[5:])])
        for key, metrics in voice_metrics.items()
    }

def calculate_category_metrics(survey_responses, category_mapping):
    """Calculate metrics for all question categories using specific functions."""
    metrics = {}

    # Map categories to their specific calculation functions
    category_functions = {
        'diversity': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'equity': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'inclusion': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'engagement': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'culture_of_engagement': lambda q: calculate_culture_of_engagement_metrics(survey_responses, q),
        'support_motivation': lambda q: calculate_support_motivation_metrics(survey_responses, q),
        'strategic_alignment': lambda q: calculate_strategic_alignment_metrics(survey_responses, q),
        'skill_development': lambda q: calculate_skill_development_metrics(survey_responses, q),
        'engagement_rate': lambda q: calculate_engagement_rate_metrics(survey_responses, q),
        'leadership': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'credibility': lambda q: calculate_credibility_metrics(survey_responses, q),
        'fairness': lambda q: calculate_fairness_metrics(survey_responses, q),
        'team_spirit': lambda q: calculate_team_spirit_metrics(survey_responses, q),
        'wellbeing': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'respect': lambda q: calculate_respect_metrics(survey_responses, q),
        'workplace_satisfaction': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'open_communication': lambda q: calculate_open_communication_metrics(survey_responses, q),
        'recognition': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'motivation': lambda q: calculate_motivation_metrics(survey_responses, q),
        'workplace_culture': lambda q: calculate_workplace_culture_metrics(survey_responses, q),
        'proud_to_work': lambda q: calculate_proud_to_work_metrics(survey_responses, q),
        'people_care': lambda q: calculate_people_care_metrics(survey_responses, q),
        'fair_promotion': lambda q: calculate_fair_promotion_metrics(survey_responses, q),
        'involvement_decision': lambda q: calculate_involvement_decision_metrics(survey_responses, q),
        'leadership_reachable': lambda q: calculate_leadership_reachable_metrics(survey_responses, q),
        'communication': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'career_development': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
        'policies': lambda q: calculate_category_sentiment_percentages(survey_responses, q),
    }

    for category, question_numbers in category_mapping.items():
        if category in category_functions:
            if category in ['culture_of_engagement', 'support_motivation', 'strategic_alignment',
                          'skill_development', 'engagement_rate', 'credibility', 'fairness',
                          'team_spirit', 'respect', 'open_communication', 'motivation',
                          'workplace_culture', 'proud_to_work', 'people_care', 'fair_promotion',
                          'involvement_decision', 'leadership_reachable']:
                # These return the full metrics dict
                metrics[category] = category_functions[category](question_numbers)
            else:
                # These return just the percentages, need to calculate score
                positive_pct, neutral_pct, negative_pct = category_functions[category](question_numbers)
                score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
                metrics[category] = {
                    'positive_pct': positive_pct,
                    'neutral_pct': neutral_pct,
                    'negative_pct': negative_pct,
                    'score': score
                }
        else:
            # Default calculation for any unmapped categories
            positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
                survey_responses, question_numbers
            )
            score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
            metrics[category] = {
                'positive_pct': positive_pct,
                'neutral_pct': neutral_pct,
                'negative_pct': negative_pct,
                'score': score
            }

    return metrics

def calculate_all_custom_metrics(email, survey_responses, num_surveys=None):
    """Calculate all metrics for custom survey questions."""
    # Get dynamic question mappings
    category_mapping, voice_mapping = get_dynamic_question_mapping(email)

    # Calculate basic metrics
    total_responses = calculate_total_responses(survey_responses)
    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
    dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    response_rate = Decimal('0.00')
    if num_surveys:
        response_rate = calculate_response_rate(total_responses, num_surveys)

    # Calculate all category metrics
    category_metrics = calculate_category_metrics(survey_responses, category_mapping)

    # Calculate demographic metrics
    department_metrics = calculate_department_metrics(survey_responses)
    experience_metrics = calculate_experience_metrics(survey_responses)
    gender_metrics = calculate_gender_metrics(survey_responses)

    # Calculate role metrics using engagement questions
    engagement_questions = category_mapping.get('engagement', [])
    role_metrics = calculate_role_engagement_metrics(survey_responses, engagement_questions)

    # Calculate voice metrics
    voice_metrics = calculate_voice_metrics(survey_responses, voice_mapping)

    return {
        'overall': {
            'total_responses': total_responses,
            'response_rate': response_rate,
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'dei_score': dei_score
        },
        'categories': category_metrics,
        'departments': department_metrics,
        'experiences': experience_metrics,
        'genders': gender_metrics,
        'roles': role_metrics,
        'voices': voice_metrics,
        'category_mapping': category_mapping,
        'voice_mapping': voice_mapping
    }
