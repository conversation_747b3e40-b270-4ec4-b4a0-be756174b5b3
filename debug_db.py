"""
Debug script to check database updates
"""

import mysql.connector

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def check_database_records():
    """Check what records exist in the database"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        email = "<EMAIL>"
        form_url = f"/test/{email}"
        
        print(f"=== CHECKING RECORDS FOR {form_url} ===")
        
        # Check companyscore table
        cursor.execute("SELECT * FROM companyscore WHERE survey_url = %s", (form_url,))
        companyscore_records = cursor.fetchall()
        print(f"companyscore records found: {len(companyscore_records)}")
        
        if companyscore_records:
            # Get column names
            cursor.execute("DESCRIBE companyscore")
            columns = [col[0] for col in cursor.fetchall()]
            print("companyscore columns:", columns[:10], "... (showing first 10)")
            
            record = companyscore_records[0]
            print(f"Sample values: survey_url={record[columns.index('survey_url')] if 'survey_url' in columns else 'N/A'}")
            print(f"Sample values: dei_score={record[columns.index('dei_score')] if 'dei_score' in columns else 'N/A'}")
            print(f"Sample values: total_responses={record[columns.index('total_responses')] if 'total_responses' in columns else 'N/A'}")
        
        # Check action_table_scores table
        cursor.execute("SELECT * FROM action_table_scores WHERE survey_url = %s", (form_url,))
        action_records = cursor.fetchall()
        print(f"action_table_scores records found: {len(action_records)}")
        
        if action_records:
            # Get column names
            cursor.execute("DESCRIBE action_table_scores")
            columns = [col[0] for col in cursor.fetchall()]
            print("action_table_scores columns:", columns[:10], "... (showing first 10)")
            
            record = action_records[0]
            print(f"Sample values: survey_url={record[columns.index('survey_url')] if 'survey_url' in columns else 'N/A'}")
            print(f"Sample values: communication_score={record[columns.index('communication_score')] if 'communication_score' in columns else 'N/A'}")
        
        # Check student_data table
        cursor.execute("SELECT COUNT(*) FROM student_data WHERE form_url = %s", (form_url,))
        student_count = cursor.fetchone()[0]
        print(f"student_data records found: {student_count}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_database_records()
