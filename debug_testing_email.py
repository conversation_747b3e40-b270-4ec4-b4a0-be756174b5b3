"""
Debug script to <NAME_EMAIL> is not updating tables
"""

import mysql.connector

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def debug_testing_email():
    """<NAME_EMAIL> email issue"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        email = "<EMAIL>"
        form_url = f"/test/{email}"
        
        print(f"=== DEBUGGING {email} ===")
        
        # 1. Check student_data table
        print("\n1. STUDENT_DATA TABLE:")
        cursor.execute("""
            SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number, question_text, selected_text
            FROM student_data 
            WHERE form_url = %s
            ORDER BY question_number
        """, (form_url,))
        
        student_data = cursor.fetchall()
        print(f"Found {len(student_data)} responses in student_data")
        
        for row in student_data:
            print(f"  Q{row[6]}: {row[1]} - {row[7][:50]}... -> {row[8]}")
        
        # 2. Check employee_questions table
        print("\n2. EMPLOYEE_QUESTIONS TABLE:")
        cursor.execute("""
            SELECT question_number, question_text
            FROM employee_questions 
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        
        questions = cursor.fetchall()
        print(f"Found {len(questions)} questions in employee_questions")
        
        for q_num, q_text in questions:
            print(f"  Q{q_num}: {q_text[:60]}...")
        
        # 3. Check companyscore table
        print("\n3. COMPANYSCORE TABLE:")
        cursor.execute("""
            SELECT survey_url, dei_score, total_responses, response_rate, diversity_score, equity_score, inclusion_score
            FROM companyscore
            WHERE survey_url = %s
        """, (form_url,))

        company_result = cursor.fetchone()
        if company_result:
            print(f"  ✅ Found record: dei_score={company_result[1]}, total_responses={company_result[2]}, response_rate={company_result[3]}")
            print(f"     diversity_score={company_result[4]}, equity_score={company_result[5]}, inclusion_score={company_result[6]}")
        else:
            print("  ❌ No record found in companyscore table")

        # 4. Check action_table_scores table
        print("\n4. ACTION_TABLE_SCORES TABLE:")
        cursor.execute("""
            SELECT survey_url, communication_score, leadership_score, recognition_rewards_score
            FROM action_table_scores
            WHERE survey_url = %s
        """, (form_url,))

        action_result = cursor.fetchone()
        if action_result:
            print(f"  ✅ Found record: communication_score={action_result[1]}, leadership_score={action_result[2]}, recognition_rewards_score={action_result[3]}")
        else:
            print("  ❌ No record found in action_table_scores table")
        
        # 5. Check if metrics calculation would work
        if student_data:
            print("\n5. TESTING METRICS CALCULATION:")
            import pandas as pd
            from custom_survey_metrics import calculate_all_custom_metrics
            from custom_action_calculations import calculate_all_action_metrics
            
            # Convert to DataFrame
            survey_responses = pd.DataFrame(student_data, columns=[
                'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number', 'question_text', 'selected_text'
            ])
            
            print(f"  DataFrame shape: {survey_responses.shape}")
            print(f"  Unique respondents: {survey_responses['unique_id'].nunique()}")
            print(f"  Sentiment distribution:")
            print(f"    {survey_responses['predicted_sentiment'].value_counts().to_dict()}")
            
            try:
                # Test survey metrics calculation
                survey_metrics = calculate_all_custom_metrics(email, survey_responses)
                print(f"  Survey metrics calculated successfully")
                print(f"    DEI score: {survey_metrics['overall']['dei_score']}")
                print(f"    Total responses: {survey_metrics['overall']['total_responses']}")
                
                # Test action metrics calculation
                action_metrics = calculate_all_action_metrics(email, survey_responses)
                print(f"  Action metrics calculated successfully")
                print(f"    Action categories: {list(action_metrics['action_metrics'].keys())}")
                
                # Show some sample scores
                for category, metrics in list(action_metrics['action_metrics'].items())[:3]:
                    print(f"    {category}: score={metrics['score']}")
                
            except Exception as calc_error:
                print(f"  Error in metrics calculation: {calc_error}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    debug_testing_email()
