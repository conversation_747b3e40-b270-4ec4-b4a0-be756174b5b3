"""
Storage functions for existing companyscore and action_table_scores tables

This module handles storing calculated metrics in the existing database tables
used by the reference Survey implementation.
"""

import mysql.connector
from decimal import Decimal

def get_db_connection():
    """Create and return a database connection"""
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

def store_survey_metrics_in_companyscore(email, survey_metrics):
    """Store survey metrics in companyscore table"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        overall = survey_metrics['overall']
        categories = survey_metrics['categories']
        departments = survey_metrics['departments']
        experiences = survey_metrics['experiences']
        genders = survey_metrics['genders']
        roles = survey_metrics['roles']
        voices = survey_metrics['voices']
        
        # Create a survey_url identifier for this custom survey
        survey_url = f"/test/{email}"
        
        # Insert/Update basic metrics in companyscore
        cursor.execute('''
            INSERT INTO companyscore (
                company_name, industry_type, country, product_name, leader_name, leader_role, contact_email,
                survey_start_date, survey_end_date, payment_status, survey_url, num_surveys, total_responses,
                response_rate, dei_positive_pct, dei_neutral_pct, dei_negative_pct, dei_score
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                total_responses = VALUES(total_responses),
                response_rate = VALUES(response_rate),
                dei_positive_pct = VALUES(dei_positive_pct),
                dei_neutral_pct = VALUES(dei_neutral_pct),
                dei_negative_pct = VALUES(dei_negative_pct),
                dei_score = VALUES(dei_score)
        ''', (
            f"Custom Survey - {email}", "Custom", "Custom", "Custom Survey", 
            "Custom", "Custom", email, "2024-01-01", "2024-12-31", "Custom", 
            survey_url, 1, overall['total_responses'], overall['response_rate'],
            overall['positive_pct'], overall['neutral_pct'], overall['negative_pct'], overall['dei_score']
        ))
        
        # Update DEI category metrics
        if 'diversity' in categories:
            diversity_metrics = categories['diversity']
            cursor.execute('''
                UPDATE companyscore SET
                diversity_score = %s, diversity_positive_pct = %s, diversity_neutral_pct = %s, diversity_negative_pct = %s
                WHERE survey_url = %s
            ''', (
                diversity_metrics['score'], diversity_metrics['positive_pct'], 
                diversity_metrics['neutral_pct'], diversity_metrics['negative_pct'], survey_url
            ))
        
        if 'equity' in categories:
            equity_metrics = categories['equity']
            cursor.execute('''
                UPDATE companyscore SET
                equity_score = %s, equity_positive_pct = %s, equity_neutral_pct = %s, equity_negative_pct = %s
                WHERE survey_url = %s
            ''', (
                equity_metrics['score'], equity_metrics['positive_pct'], 
                equity_metrics['neutral_pct'], equity_metrics['negative_pct'], survey_url
            ))
        
        if 'inclusion' in categories:
            inclusion_metrics = categories['inclusion']
            cursor.execute('''
                UPDATE companyscore SET
                inclusion_score = %s, inclusion_positive_pct = %s, inclusion_neutral_pct = %s, inclusion_negative_pct = %s
                WHERE survey_url = %s
            ''', (
                inclusion_metrics['score'], inclusion_metrics['positive_pct'], 
                inclusion_metrics['neutral_pct'], inclusion_metrics['negative_pct'], survey_url
            ))
        
        # Update engagement-related metrics using correct column names
        if 'culture_of_engagement' in categories:
            culture_engagement_metrics = categories['culture_of_engagement']
            cursor.execute('''
                UPDATE companyscore SET
                culture_engagement_score = %s, culture_engagement_positive_pct = %s,
                culture_engagement_neutral_pct = %s, culture_engagement_negative_pct = %s
                WHERE survey_url = %s
            ''', (
                culture_engagement_metrics['score'], culture_engagement_metrics['positive_pct'],
                culture_engagement_metrics['neutral_pct'], culture_engagement_metrics['negative_pct'], survey_url
            ))
        
        # Update other category metrics
        category_updates = []
        category_values = []
        
        for category, metrics in categories.items():
            # Only update categories that definitely exist in companyscore table (verified from DESCRIBE)
            if category in ['leadership', 'workplace_satisfaction', 'recognition', 'policies',
                          'workplace_culture', 'proud_to_work', 'people_care', 'fair_promotion',
                          'involvement_decision', 'leadership_reachable', 'credibility', 'fairness',
                          'team_spirit', 'respect', 'open_communication', 'motivation',
                          'support_motivation', 'strategic_alignment', 'skill_development', 'engagement_rate']:
                category_updates.extend([
                    f"{category}_score = %s",
                    f"{category}_positive_pct = %s",
                    f"{category}_neutral_pct = %s",
                    f"{category}_negative_pct = %s"
                ])
                category_values.extend([
                    metrics['score'], metrics['positive_pct'],
                    metrics['neutral_pct'], metrics['negative_pct']
                ])
            elif category == 'wellbeing':
                # Map wellbeing to well_being (with underscore)
                category_updates.extend([
                    "well_being_score = %s",
                    "well_being_positive_pct = %s",
                    "well_being_neutral_pct = %s",
                    "well_being_negative_pct = %s"
                ])
                category_values.extend([
                    metrics['score'], metrics['positive_pct'],
                    metrics['neutral_pct'], metrics['negative_pct']
                ])

        
        if category_updates:
            update_query = f'''
                UPDATE companyscore SET
                {', '.join(category_updates)}
                WHERE survey_url = %s
            '''
            category_values.append(survey_url)
            cursor.execute(update_query, category_values)
        
        # Update department metrics
        dept_updates = []
        dept_values = []
        
        for dept_key, value in departments.items():
            dept_updates.append(f"{dept_key} = %s")
            dept_values.append(value)
        
        if dept_updates:
            dept_query = f'''
                UPDATE companyscore SET
                {', '.join(dept_updates)}
                WHERE survey_url = %s
            '''
            dept_values.append(survey_url)
            cursor.execute(dept_query, dept_values)
        
        # Update experience metrics (handle database schema inconsistency)
        exp_updates = []
        exp_values = []

        for exp_key, value in experiences.items():
            # Handle the specific database schema issue for exp_30dto1_negative_pct
            if exp_key == 'exp_30dto1_negative_pct':
                exp_updates.append("exp_30to1_negative_pct = %s")  # Database has typo
            else:
                exp_updates.append(f"{exp_key} = %s")
            exp_values.append(value)
        
        if exp_updates:
            exp_query = f'''
                UPDATE companyscore SET
                {', '.join(exp_updates)}
                WHERE survey_url = %s
            '''
            exp_values.append(survey_url)
            cursor.execute(exp_query, exp_values)
        
        # Update gender metrics
        gender_updates = []
        gender_values = []
        
        for gender_key, value in genders.items():
            gender_updates.append(f"{gender_key} = %s")
            gender_values.append(value)
        
        if gender_updates:
            gender_query = f'''
                UPDATE companyscore SET
                {', '.join(gender_updates)}
                WHERE survey_url = %s
            '''
            gender_values.append(survey_url)
            cursor.execute(gender_query, gender_values)
        
        # Update role metrics
        role_updates = []
        role_values = []
        
        for role_key, value in roles.items():
            role_updates.append(f"{role_key} = %s")
            role_values.append(value)
        
        if role_updates:
            role_query = f'''
                UPDATE companyscore SET
                {', '.join(role_updates)}
                WHERE survey_url = %s
            '''
            role_values.append(survey_url)
            cursor.execute(role_query, role_values)
        
        # Update voice metrics
        voice_updates = []
        voice_values = []
        
        for voice_key, voice_metrics in voices.items():
            # voice_metrics is a tuple: (positive_pct, neutral_pct, negative_pct, score, priority)
            voice_num = voice_key.replace('voice', '')
            voice_updates.extend([
                f"emp_voice{voice_num}_positive_pct = %s",
                f"emp_voice{voice_num}_neutral_pct = %s",
                f"emp_voice{voice_num}_negative_pct = %s",
                f"emp_voice{voice_num}_score = %s",
                f"emp_voice{voice_num}_priority = %s"
            ])
            voice_values.extend([
                voice_metrics[0], voice_metrics[1], voice_metrics[2], 
                voice_metrics[3], voice_metrics[4]
            ])
        
        if voice_updates:
            voice_query = f'''
                UPDATE companyscore SET
                {', '.join(voice_updates)}
                WHERE survey_url = %s
            '''
            voice_values.append(survey_url)
            cursor.execute(voice_query, voice_values)
        
        conn.commit()
        print(f"Survey metrics stored successfully in companyscore for {email}")
        
    except Exception as e:
        print(f"Error storing survey metrics: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def store_action_metrics_in_action_table(email, action_metrics):
    """Store action metrics in action_table_scores table"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        action_data = action_metrics['action_metrics']
        survey_url = f"/test/{email}"
        
        # Insert/Update basic record
        cursor.execute('''
            INSERT INTO action_table_scores (company_name, survey_url)
            VALUES (%s, %s)
            ON DUPLICATE KEY UPDATE company_name = VALUES(company_name), survey_url = VALUES(survey_url)
        ''', (f"Custom Survey - {email}", survey_url))
        
        # Update action category metrics
        action_updates = []
        action_values = []
        
        for action_category, metrics in action_data.items():
            # Map category names to database column names (based on reference implementation)
            if action_category == 'communication':
                action_updates.extend([
                    "communication_positive_pct = %s",
                    "communication_neutral_pct = %s",
                    "communication_negative_pct = %s",
                    "communication_score = %s"
                ])
            elif action_category == 'leadership_effectiveness':
                action_updates.extend([
                    "leadership_positive_pct = %s",
                    "leadership_neutral_pct = %s",
                    "leadership_negative_pct = %s",
                    "leadership_score = %s"
                ])
            elif action_category == 'work_life_balance':
                action_updates.extend([
                    "worklife_balance_positive_pct = %s",
                    "worklife_balance_neutral_pct = %s",
                    "worklife_balance_negative_pct = %s",
                    "worklife_balance_score = %s"
                ])
            elif action_category == 'career_development':
                action_updates.extend([
                    "career_development_positive_pct = %s",
                    "career_development_neutral_pct = %s",
                    "career_development_negative_pct = %s",
                    "career_development_score = %s"
                ])
            elif action_category == 'recognition_rewards':
                action_updates.extend([
                    "recognition_rewards_positive_pct = %s",
                    "recognition_rewards_neutral_pct = %s",
                    "recognition_rewards_negative_pct = %s",
                    "recognition_rewards_score = %s"
                ])
            elif action_category == 'employee_engagement':
                action_updates.extend([
                    "employee_engagement_positive_pct = %s",
                    "employee_engagement_neutral_pct = %s",
                    "employee_engagement_negative_pct = %s",
                    "employee_engagement_score = %s"
                ])
            elif action_category == 'workplace_environment':
                action_updates.extend([
                    "workplace_environment_positive_pct = %s",
                    "workplace_environment_neutral_pct = %s",
                    "workplace_environment_negative_pct = %s",
                    "workplace_environment_score = %s"
                ])
            elif action_category == 'inclusion_diversity':
                action_updates.extend([
                    "inclusion_diversity_positive_pct = %s",
                    "inclusion_diversity_neutral_pct = %s",
                    "inclusion_diversity_negative_pct = %s",
                    "inclusion_diversity_score = %s"
                ])
            elif action_category == 'compensation_transparency':
                action_updates.extend([
                    "compensation_transparency_positive_pct = %s",
                    "compensation_transparency_neutral_pct = %s",
                    "compensation_transparency_negative_pct = %s",
                    "compensation_transparency_score = %s"
                ])
            elif action_category == 'feedback_mechanisms':
                action_updates.extend([
                    "feedback_mechanisms_positive_pct = %s",
                    "feedback_mechanisms_neutral_pct = %s",
                    "feedback_mechanisms_negative_pct = %s",
                    "feedback_mechanisms_score = %s"
                ])
            elif action_category == 'organizational_transparency':
                action_updates.extend([
                    "organizational_transparency_positive_pct = %s",
                    "organizational_transparency_neutral_pct = %s",
                    "organizational_transparency_negative_pct = %s",
                    "organizational_transparency_score = %s"
                ])
            elif action_category == 'manager_employee_relationship':
                action_updates.extend([
                    "manager_employee_relationship_positive_pct = %s",
                    "manager_employee_relationship_neutral_pct = %s",
                    "manager_employee_relationship_negative_pct = %s",
                    "manager_employee_relationship_score = %s"
                ])
            elif action_category == 'psychological_safety':
                action_updates.extend([
                    "psychological_safety_positive_pct = %s",
                    "psychological_safety_neutral_pct = %s",
                    "psychological_safety_negative_pct = %s",
                    "psychological_safety_score = %s"
                ])
            elif action_category == 'mission_values_alignment':
                action_updates.extend([
                    "mission_values_alignment_positive_pct = %s",
                    "mission_values_alignment_neutral_pct = %s",
                    "mission_values_alignment_negative_pct = %s",
                    "mission_values_alignment_score = %s"
                ])
            elif action_category == 'innovation_creativity':
                action_updates.extend([
                    "innovation_creativity_positive_pct = %s",
                    "innovation_creativity_neutral_pct = %s",
                    "innovation_creativity_negative_pct = %s",
                    "innovation_creativity_score = %s"
                ])

            action_values.extend([
                metrics['positive_pct'], metrics['neutral_pct'],
                metrics['negative_pct'], metrics['score']
            ])
        
        if action_updates:
            update_query = f'''
                UPDATE action_table_scores SET
                {', '.join(action_updates)}
                WHERE survey_url = %s
            '''
            action_values.append(survey_url)
            cursor.execute(update_query, action_values)
        
        conn.commit()
        print(f"Action metrics stored successfully in action_table_scores for {email}")
        
    except Exception as e:
        print(f"Error storing action metrics: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def store_all_metrics_in_existing_tables(email, survey_metrics, action_metrics):
    """Store both survey and action metrics in existing tables"""
    try:
        store_survey_metrics_in_companyscore(email, survey_metrics)
        store_action_metrics_in_action_table(email, action_metrics)
        print(f"All metrics stored successfully in existing tables for {email}")
    except Exception as e:
        print(f"Error storing all metrics in existing tables: {e}")
