<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survey Questions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .question h3 {
            color: #555;
            margin-bottom: 15px;
        }
        .options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .option {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .option:hover {
            background-color: #e9ecef;
        }
        .option input[type="radio"] {
            margin-right: 10px;
        }
        button {
            background-color: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        button:hover {
            background-color: #218838;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .progress-bar {
            background-color: #007bff;
            height: 20px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Survey Questions</h1>
        
        <div class="progress">
            <div class="progress-bar" style="width: 100%;">
                {{ questions|length }} Questions
            </div>
        </div>

        <form method="POST">
            {% for question in questions %}
            <div class="question">
                <h3>{{ question.question_number }}. {{ question.question_text }}</h3>
                
                <!-- Hidden field for question text -->
                <input type="hidden" name="question_text_{{ question.question_number }}" value="{{ question.question_text }}">
                
                <div class="options">
                    {% if question.option_1 %}
                    <label class="option">
                        <input type="radio" name="question_{{ question.question_number }}" value="{{ question.option_1 }}" required>
                        {{ question.option_1 }}
                    </label>
                    {% endif %}
                    
                    {% if question.option_2 %}
                    <label class="option">
                        <input type="radio" name="question_{{ question.question_number }}" value="{{ question.option_2 }}" required>
                        {{ question.option_2 }}
                    </label>
                    {% endif %}
                    
                    {% if question.option_3 %}
                    <label class="option">
                        <input type="radio" name="question_{{ question.question_number }}" value="{{ question.option_3 }}" required>
                        {{ question.option_3 }}
                    </label>
                    {% endif %}
                    
                    {% if question.option_4 %}
                    <label class="option">
                        <input type="radio" name="question_{{ question.question_number }}" value="{{ question.option_4 }}" required>
                        {{ question.option_4 }}
                    </label>
                    {% endif %}
                </div>
            </div>
            {% endfor %}

            <button type="submit">Submit Survey</button>
        </form>
    </div>

    <script>
        // Add click functionality to option labels
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });
    </script>
</body>
</html>
