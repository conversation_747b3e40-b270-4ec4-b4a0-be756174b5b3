"""
Test action metrics calculation
"""

import mysql.connector
import pandas as pd
from custom_action_calculations import calculate_all_action_metrics

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def test_action_metrics():
    """Test action metrics calculation"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        email = "<EMAIL>"
        form_url = f"/test/{email}"
        
        # Get survey responses
        cursor.execute("""
            SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
            FROM student_data 
            WHERE form_url = %s
        """, (form_url,))
        
        survey_data = cursor.fetchall()
        print(f"Found {len(survey_data)} survey responses")
        
        if survey_data:
            # Convert to DataFrame
            survey_responses = pd.DataFrame(survey_data, columns=[
                'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
            ])
            
            print("Survey responses:")
            print(survey_responses)
            
            # Calculate action metrics
            action_metrics = calculate_all_action_metrics(email, survey_responses)
            
            print(f"\nAction metrics keys: {list(action_metrics['action_metrics'].keys())}")
            print(f"Action mapping: {action_metrics['action_mapping']}")
            
            # Show sample metrics
            for category, metrics in action_metrics['action_metrics'].items():
                print(f"{category}: score={metrics['score']}, pos={metrics['positive_pct']}, neg={metrics['negative_pct']}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_action_metrics()
