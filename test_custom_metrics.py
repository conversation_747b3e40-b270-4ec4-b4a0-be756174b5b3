"""
Test script for custom survey metrics system
"""

import mysql.connector
import pandas as pd
from decimal import Decimal

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def create_test_questions(email):
    """Create test questions for an email"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        # Delete existing questions for this email
        cursor.execute("DELETE FROM employee_questions WHERE email = %s", (email,))
        
        # Insert test questions
        test_questions = [
            (1, "Do you feel valued and appreciated for your work?", "Option 1", "Option 2", "Option 3", "Option 4"),
            (2, "Does your manager support fairness and equal treatment?", "Always", "Sometimes", "Rarely", "Never"),
            (3, "Do you feel included in team discussions?", "Yes", "Mostly", "Sometimes", "No"),
            (4, "How effective is communication in your organization?", "Very Good", "Good", "Fair", "Poor"),
            (5, "Do you have opportunities for career development?", "Many", "Some", "Few", "None")
        ]

        for q_num, q_text, opt1, opt2, opt3, opt4 in test_questions:
            cursor.execute("""
                INSERT INTO employee_questions
                (email, question_number, question_text, option_1, option_2, option_3, option_4)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (email, q_num, q_text, opt1, opt2, opt3, opt4))
        
        conn.commit()
        print(f"Created {len(test_questions)} test questions for {email}")
        
    except Exception as e:
        print(f"Error creating test questions: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def create_test_responses(email):
    """Create test survey responses"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        form_url = f"/test/{email}"
        
        # Delete existing responses for this email
        cursor.execute("DELETE FROM student_data WHERE form_url = %s", (form_url,))
        
        # Insert test responses
        test_responses = [
            ("user1", "Male", "25-35", "1-3 years", "Junior Staff", "Technical", 1, "Do you feel valued and appreciated for your work?", "Option 2", "Positive", "Good response"),
            ("user1", "Male", "25-35", "1-3 years", "Junior Staff", "Technical", 2, "Does your manager support fairness and equal treatment?", "Sometimes", "Neutral", "Neutral response"),
            ("user2", "Female", "35-45", "3-5 years", "Senior Staff", "HR", 1, "Do you feel valued and appreciated for your work?", "Option 1", "Positive", "Very positive"),
            ("user2", "Female", "35-45", "3-5 years", "Senior Staff", "HR", 2, "Does your manager support fairness and equal treatment?", "Always", "Positive", "Excellent"),
            ("user3", "Other", "25-35", "0-1 year", "Trainee", "Sales", 1, "Do you feel valued and appreciated for your work?", "Option 3", "Negative", "Not satisfied"),
        ]
        
        for unique_id, gender, age_group, tenure_group, role, department, q_num, q_text, selected, sentiment, reason in test_responses:
            cursor.execute("""
                INSERT INTO student_data 
                (unique_id, gender, age_group, tenure_group, role, department, 
                 question_number, question_text, selected_text, form_url, predicted_sentiment, reason)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (unique_id, gender, age_group, tenure_group, role, department, 
                  q_num, q_text, selected, form_url, sentiment, reason))
        
        conn.commit()
        print(f"Created {len(test_responses)} test responses for {email}")
        
    except Exception as e:
        print(f"Error creating test responses: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def test_metrics_calculation(email):
    """Test the metrics calculation"""
    try:
        from custom_survey_metrics import calculate_all_custom_metrics
        from custom_action_calculations import calculate_all_action_metrics
        from custom_metrics_storage import store_all_custom_metrics
        
        # Get survey responses
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        form_url = f"/test/{email}"
        cursor.execute("""
            SELECT unique_id, predicted_sentiment, department, tenure_group, role, gender, question_number
            FROM student_data 
            WHERE form_url = %s
        """, (form_url,))
        
        survey_data = cursor.fetchall()
        cursor.close()
        conn.close()
        
        if survey_data:
            # Convert to DataFrame
            survey_responses = pd.DataFrame(survey_data, columns=[
                'unique_id', 'predicted_sentiment', 'department', 'tenure_group', 'role', 'gender', 'question_number'
            ])
            
            print(f"Survey responses DataFrame shape: {survey_responses.shape}")
            print(f"Unique responses: {survey_responses['unique_id'].nunique()}")
            
            # Calculate metrics
            survey_metrics = calculate_all_custom_metrics(email, survey_responses)
            action_metrics = calculate_all_action_metrics(email, survey_responses)
            
            print("\n=== SURVEY METRICS ===")
            print(f"Overall metrics: {survey_metrics['overall']}")
            print(f"Categories found: {list(survey_metrics['categories'].keys())}")
            print(f"Department metrics: {len(survey_metrics['departments'])} departments")
            print(f"Voice metrics: {len(survey_metrics['voices'])} voice statements")
            
            print("\n=== ACTION METRICS ===")
            print(f"Action categories: {list(action_metrics['action_metrics'].keys())}")
            
            # Store metrics
            store_all_custom_metrics(email, form_url, survey_metrics, action_metrics)
            
            print(f"\n✅ Metrics calculation and storage completed for {email}")
            return True
            
        else:
            print(f"❌ No survey data found for {email}")
            return False
            
    except Exception as e:
        print(f"❌ Error in metrics calculation: {e}")
        return False

def verify_stored_metrics(email):
    """Verify metrics are stored correctly"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        form_url = f"/test/{email}"
        
        # Check survey metrics
        cursor.execute("""
            SELECT total_responses, dei_score, diversity_score, equity_score, inclusion_score
            FROM custom_survey_scores 
            WHERE email = %s AND form_url = %s
        """, (email, form_url))
        
        survey_result = cursor.fetchone()
        
        # Check action metrics
        cursor.execute("""
            SELECT communication_score, career_development_score
            FROM custom_action_scores 
            WHERE email = %s AND form_url = %s
        """, (email, form_url))
        
        action_result = cursor.fetchone()
        
        print(f"\n=== STORED METRICS VERIFICATION ===")
        if survey_result:
            print(f"Survey metrics found: Total responses={survey_result[0]}, DEI score={survey_result[1]}")
            print(f"Category scores: Diversity={survey_result[2]}, Equity={survey_result[3]}, Inclusion={survey_result[4]}")
        else:
            print("❌ No survey metrics found in database")
        
        if action_result:
            print(f"Action metrics found: Communication={action_result[0]}, Career Development={action_result[1]}")
        else:
            print("❌ No action metrics found in database")
        
        return survey_result is not None or action_result is not None
        
    except Exception as e:
        print(f"❌ Error verifying stored metrics: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def run_full_test():
    """Run complete test suite"""
    test_email = "<EMAIL>"
    
    print("🧪 Starting Custom Survey Metrics Test")
    print("=" * 50)
    
    # Step 1: Create test questions
    print("1. Creating test questions...")
    create_test_questions(test_email)
    
    # Step 2: Create test responses
    print("2. Creating test responses...")
    create_test_responses(test_email)
    
    # Step 3: Test metrics calculation
    print("3. Testing metrics calculation...")
    calc_success = test_metrics_calculation(test_email)
    
    # Step 4: Verify stored metrics
    print("4. Verifying stored metrics...")
    verify_success = verify_stored_metrics(test_email)
    
    # Summary
    print("\n" + "=" * 50)
    print("🧪 TEST SUMMARY")
    print(f"Metrics Calculation: {'✅ PASSED' if calc_success else '❌ FAILED'}")
    print(f"Metrics Storage: {'✅ PASSED' if verify_success else '❌ FAILED'}")
    
    if calc_success and verify_success:
        print(f"\n🎉 All tests passed! You can view results at: /metrics/{test_email}")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    run_full_test()
