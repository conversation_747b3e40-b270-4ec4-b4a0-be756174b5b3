"""
Test the Custom plan logic
"""

import mysql.connector

DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def test_custom_plan():
    """Test if Custom plan <NAME_EMAIL>"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    try:
        email = "<EMAIL>"

        print(f"=== TESTING CUSTOM PLAN FOR {email} ===")

        # First check if invoicek table exists
        cursor.execute("SHOW TABLES LIKE 'invoicek'")
        if not cursor.fetchone():
            print("❌ invoicek table does not exist!")
            print("Checking what tables exist...")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print("Available tables:")
            for table in tables:
                print(f"   {table[0]}")
            return

        # Check if Custom plan exists
        cursor.execute("""
            SELECT company_name, industry_type, country, product_name, leaderName, leaderRole,
                   surveyStartDate, surveyEndDate, num_surveys, plan, contactEmail
            FROM invoicek
            WHERE contactEmail = %s AND plan = %s
        """, (email, "Custom"))
        
        invoice_data = cursor.fetchone()
        
        if invoice_data:
            print("✅ Custom plan found!")
            company_name, industry_type, country, product_name, leader_name, leader_role, survey_start_date, survey_end_date, num_surveys, plan, contact_email = invoice_data
            print(f"   Company: {company_name}")
            print(f"   Industry: {industry_type}")
            print(f"   Country: {country}")
            print(f"   Leader: {leader_name} ({leader_role})")
            print(f"   Num surveys: {num_surveys}")
            print(f"   Plan: {plan}")
            print(f"   Contact: {contact_email}")
        else:
            print("❌ No Custom plan found!")
            print("Checking what plans exist for this email...")
            
            cursor.execute("""
                SELECT plan, company_name, num_surveys
                FROM invoicek
                WHERE contactEmail = %s
            """, (email,))
            
            all_plans = cursor.fetchall()
            if all_plans:
                print("Found these plans:")
                for plan_data in all_plans:
                    print(f"   Plan: {plan_data[0]}, Company: {plan_data[1]}, Surveys: {plan_data[2]}")
            else:
                print("No plans found for this email at all!")
                
                # Check if we need to create a Custom plan entry
                print("\nTo fix this, you need to add a Custom plan entry in invoicek table:")
                print(f"INSERT INTO invoicek (contactEmail, plan, company_name, industry_type, country, product_name, leaderName, leaderRole, num_surveys, surveyStartDate, surveyEndDate)")
                print(f"VALUES ('{email}', 'Custom', 'Test Company', 'Technology', 'India', 'Custom Survey', 'Test Leader', 'Manager', 100, '2024-01-01', '2024-12-31');")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_custom_plan()
